@echo off
echo 🏥 Healthcare Emerging Expert Predictor - Streamlit Launcher
echo ============================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python 3.8+ and add it to PATH
    echo 💡 You can download Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "models\gradient_boosting_model.joblib" (
    echo ❌ Trained models not found. Please run the ML pipeline first:
    echo    python pipeline_orchestrator.py
    pause
    exit /b 1
)

if not exist "data\processed\feature_statistics.json" (
    echo ❌ Feature statistics not found. Please run the ML pipeline first:
    echo    python pipeline_orchestrator.py
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Install Streamlit requirements if needed
echo 📦 Checking Streamlit dependencies...
python -c "import streamlit" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 Installing Streamlit requirements...
    python -m pip install -r streamlit_app\requirements_streamlit.txt
    if %errorlevel% neq 0 (
        echo ❌ Failed to install requirements
        pause
        exit /b 1
    )
)

echo ✅ Dependencies ready
echo.

REM Run integration test
echo 🧪 Running integration test...
python test_streamlit_integration.py
if %errorlevel% neq 0 (
    echo ⚠️ Integration test failed, but continuing anyway...
    echo 💡 You may encounter issues with the application
    echo.
)

REM Launch Streamlit
echo 🚀 Launching Streamlit application...
echo 📱 Opening in your default browser...
echo 🔗 URL: http://localhost:8501
echo.
echo ⏹️ Press Ctrl+C to stop the application
echo.

cd streamlit_app
python -m streamlit run app.py

pause
