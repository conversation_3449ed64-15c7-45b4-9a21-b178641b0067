{"label_correction": {"model_type": "logistic_regression", "optimal_threshold": 0.5478402832112085, "test_accuracy": 0.6, "classification_report": {"0": {"precision": 0.9090909090909091, "recall": 0.5882352941176471, "f1-score": 0.7142857142857143, "support": 17.0}, "1": {"precision": 0.2222222222222222, "recall": 0.6666666666666666, "f1-score": 0.3333333333333333, "support": 3.0}, "accuracy": 0.6, "macro avg": {"precision": 0.5656565656565656, "recall": 0.6274509803921569, "f1-score": 0.5238095238095238, "support": 20.0}, "weighted avg": {"precision": 0.806060606060606, "recall": 0.6, "f1-score": 0.6571428571428571, "support": 20.0}}, "feature_coefficients": {"publications_count": -0.14798115129794656, "first_author_count": 0.26642670553110515, "clinical_trials_count": 0.20022505580859204, "pi_roles_count": -0.12151822709764239, "conferences_count": -0.26521803013883466, "years_since_enumeration": 0.29912683185802486}, "roc_auc": 0.5686274509803921}, "models": {"logistic_regression": {"model_config": {"class_weight": "balanced", "max_iter": 1000, "random_state": 42}, "test_accuracy": 0.7, "classification_report": {"0": {"precision": 0.8, "recall": 0.6666666666666666, "f1-score": 0.7272727272727273, "support": 12.0}, "1": {"precision": 0.6, "recall": 0.75, "f1-score": 0.6666666666666666, "support": 8.0}, "accuracy": 0.7, "macro avg": {"precision": 0.7, "recall": 0.7083333333333333, "f1-score": 0.696969696969697, "support": 20.0}, "weighted avg": {"precision": 0.7200000000000001, "recall": 0.7, "f1-score": 0.7030303030303029, "support": 20.0}}, "roc_auc": 0.71875, "feature_coefficients": {"publications_count": -0.4999454797304797, "first_author_count": 1.1001024740300482, "clinical_trials_count": 0.574275800602703, "pi_roles_count": -0.8128018572507361, "conferences_count": -0.75107920142294, "years_since_enumeration": 1.2136591631823566}, "cv_f1_mean": 0.7923076923076924, "cv_f1_std": 0.084420910939253}, "random_forest": {"model_config": {"n_estimators": 100, "random_state": 42, "class_weight": "balanced", "max_depth": null, "min_samples_split": 2, "min_samples_leaf": 1}, "test_accuracy": 0.7, "classification_report": {"0": {"precision": 0.6666666666666666, "recall": 1.0, "f1-score": 0.8, "support": 12.0}, "1": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 8.0}, "accuracy": 0.7, "macro avg": {"precision": 0.8333333333333333, "recall": 0.625, "f1-score": 0.6000000000000001, "support": 20.0}, "weighted avg": {"precision": 0.8, "recall": 0.7, "f1-score": 0.64, "support": 20.0}}, "roc_auc": 0.5729166666666667, "feature_importance": {"publications_count": 0.13138830571424764, "first_author_count": 0.23711427041998212, "clinical_trials_count": 0.11533402991474596, "pi_roles_count": 0.08645484810768073, "conferences_count": 0.15211387267240978, "years_since_enumeration": 0.27759467317093384}, "cv_f1_mean": 0.6321212121212121, "cv_f1_std": 0.07736714374415975}, "gradient_boosting": {"model_config": {"n_estimators": 100, "learning_rate": 0.1, "max_depth": 3, "random_state": 42}, "test_accuracy": 0.6, "classification_report": {"0": {"precision": 0.6428571428571429, "recall": 0.75, "f1-score": 0.6923076923076923, "support": 12.0}, "1": {"precision": 0.5, "recall": 0.375, "f1-score": 0.42857142857142855, "support": 8.0}, "accuracy": 0.6, "macro avg": {"precision": 0.5714285714285714, "recall": 0.5625, "f1-score": 0.5604395604395604, "support": 20.0}, "weighted avg": {"precision": 0.5857142857142857, "recall": 0.6, "f1-score": 0.5868131868131867, "support": 20.0}}, "roc_auc": 0.6041666666666666, "feature_importance": {"publications_count": 0.1265281788891974, "first_author_count": 0.30441520883486617, "clinical_trials_count": 0.08044886496521697, "pi_roles_count": 0.07758830020284464, "conferences_count": 0.1698151208600852, "years_since_enumeration": 0.2412043262477895}, "cv_f1_mean": 0.61004329004329, "cv_f1_std": 0.049347915166910825}}, "mislabeled_count": 22, "correction_summary": {"original_positive": "17", "corrected_positive": "39", "labels_changed": "22"}}