2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - _setup_logger:68 - Log file created: logs/pipeline_20250528_104814.log
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - __init__:49 - Pipeline orchestrator initialized
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:104 - Configuration - Pipeline:
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   name: healthcare_emerging_experts_pipeline
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   version: 1.0.0
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   stages: ['etl', 'eda', 'feature_engineering', 'modeling', 'evaluation']
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   execute_stages: {'etl': True, 'eda': True, 'feature_engineering': True, 'modeling': True, 'evaluation': True}
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - run_pipeline:62 - Starting healthcare ML pipeline execution
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - run_pipeline:68 - Pipeline stages to execute: ['etl', 'eda', 'feature_engineering', 'modeling', 'evaluation']
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:104 - Configuration - Run Configuration:
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   run_id: 20250528_104814
2025-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   timestamp: 2025-05-28T10:48:14.***********-05-28 10:48:14,706 - healthcare_pipeline - INFO - log_config_info:106 -   config_version: 1.0.0
2025-05-28 10:48:14,707 - healthcare_pipeline - INFO - log_config_info:106 -   enabled_stages: ['etl', 'eda', 'feature_engineering', 'modeling', 'evaluation']
2025-05-28 10:48:14,707 - healthcare_pipeline - INFO - log_config_info:106 -   config_file: config/pipeline_config.yaml
2025-05-28 10:48:14,707 - healthcare_pipeline - INFO - log_stage_start:77 - 
================================================================================
2025-05-28 10:48:14,707 - healthcare_pipeline - INFO - log_stage_start:78 - STARTING STAGE: ETL
2025-05-28 10:48:14,707 - healthcare_pipeline - INFO - log_stage_start:79 - ================================================================================
2025-05-28 10:48:14,707 - healthcare_pipeline - INFO - _run_etl_stage:134 - Executing ETL stage
2025-05-28 10:48:29,273 - healthcare_pipeline - INFO - log_data_info:91 - Dataset: Raw Data
2025-05-28 10:48:29,274 - healthcare_pipeline - INFO - log_data_info:92 -   Shape: (60008, 19)
2025-05-28 10:48:29,274 - healthcare_pipeline - INFO - log_data_info:94 -   Missing values: ***********-05-28 10:48:29,274 - healthcare_pipeline - INFO - log_file_operation:111 - File save [SUCCESS]: data/processed/fellow_data_raw.csv
2025-05-28 10:48:29,274 - healthcare_pipeline - INFO - log_stage_end:85 - ================================================================================
2025-05-28 10:48:29,274 - healthcare_pipeline - INFO - log_stage_end:86 - STAGE COMPLETED: ETL
2025-05-28 10:48:29,275 - healthcare_pipeline - INFO - log_stage_end:87 - ================================================================================

2025-05-28 10:48:29,275 - healthcare_pipeline - INFO - log_stage_start:77 - 
================================================================================
2025-05-28 10:48:29,275 - healthcare_pipeline - INFO - log_stage_start:78 - STARTING STAGE: EDA
2025-05-28 10:48:29,275 - healthcare_pipeline - INFO - log_stage_start:79 - ================================================================================
2025-05-28 10:48:29,275 - healthcare_pipeline - INFO - _run_eda_stage:159 - Executing EDA stage
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - _run_eda_stage:172 - EDA completed. Generated 3 visualizations
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - log_stage_end:85 - ================================================================================
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - log_stage_end:86 - STAGE COMPLETED: EDA
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - log_stage_end:87 - ================================================================================

2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - log_stage_start:77 - 
================================================================================
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - log_stage_start:78 - STARTING STAGE: FEATURE_ENGINEERING
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - log_stage_start:79 - ================================================================================
2025-05-28 10:48:31,644 - healthcare_pipeline - INFO - _run_feature_engineering_stage:183 - Executing Feature Engineering stage
2025-05-28 10:49:11,964 - healthcare_pipeline - INFO - log_data_info:91 - Dataset: Processed Data
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_data_info:92 -   Shape: (5675, 27)
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - _run_feature_engineering_stage:205 - Feature engineering completed. Created 27 features
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_stage_end:85 - ================================================================================
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_stage_end:86 - STAGE COMPLETED: FEATURE_ENGINEERING
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_stage_end:87 - ================================================================================

2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_stage_start:77 - 
================================================================================
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_stage_start:78 - STARTING STAGE: MODELING
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - log_stage_start:79 - ================================================================================
2025-05-28 10:49:11,966 - healthcare_pipeline - INFO - _run_modeling_stage:216 - Executing Modeling stage
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - _run_modeling_stage:235 - Modeling completed. Labels changed: 674
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - _run_modeling_stage:236 - Models trained: 3
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_end:85 - ================================================================================
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_end:86 - STAGE COMPLETED: MODELING
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_end:87 - ================================================================================

2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_start:77 - 
================================================================================
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_start:78 - STARTING STAGE: EVALUATION
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_start:79 - ================================================================================
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - _run_evaluation_stage:247 - Executing Evaluation stage
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - _run_evaluation_stage:272 - Evaluation stage completed (placeholder implementation)
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_end:85 - ================================================================================
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_end:86 - STAGE COMPLETED: EVALUATION
2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - log_stage_end:87 - ================================================================================

2025-05-28 10:49:15,441 - healthcare_pipeline - INFO - run_pipeline:85 - Pipeline execution completed successfully
