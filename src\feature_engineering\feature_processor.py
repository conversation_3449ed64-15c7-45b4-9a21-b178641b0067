"""
Feature Engineering module for the Healthcare ML Pipeline.
Handles feature aggregation, preprocessing, and transformation.
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import logging
import json

from ..utils.logger import get_logger, log_function_call
from ..utils.config_manager import ConfigManager


class FeatureProcessor:
    """
    Handles feature engineering, aggregation, and preprocessing.
    """

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the feature processor.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.fe_config = config_manager.get_stage_config('feature_engineering')
        self.features_config = config_manager.get_features_config()
        self.logger = get_logger(__name__)

        # Initialize scalers and imputers
        self.scaler = None
        self.imputer = None

    @log_function_call()
    def aggregate_hcp_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Aggregate features at the HCP level.

        Args:
            df: Raw data DataFrame

        Returns:
            Aggregated features DataFrame
        """
        self.logger.info("Starting HCP-level feature aggregation")

        group_by_col = self.fe_config.get('aggregation', {}).get('group_by_column', 'mdm_id')

        if group_by_col not in df.columns:
            raise ValueError(f"Group by column '{group_by_col}' not found in DataFrame")

        # Get unique HCPs
        unique_hcps = df[group_by_col].unique()
        self.logger.info(f"Processing {len(unique_hcps)} unique HCPs")

        hcp_features = {}

        for hcp_id in unique_hcps:
            hcp_data = df[df[group_by_col] == hcp_id]
            features = self._calculate_hcp_features(hcp_data)
            hcp_features[hcp_id] = features

        # Convert to DataFrame
        hcp_features_df = pd.DataFrame.from_dict(hcp_features, orient='index')
        hcp_features_df.reset_index(inplace=True)
        hcp_features_df.rename(columns={'index': group_by_col}, inplace=True)

        self.logger.info(f"Feature aggregation completed. Shape: {hcp_features_df.shape}")

        return hcp_features_df

    def _calculate_hcp_features(self, hcp_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate features for a single HCP.

        Args:
            hcp_data: Data for a single HCP

        Returns:
            Dictionary of calculated features
        """
        features = {}

        # Basic information (should be the same for all rows of this HCP)
        features['emerging_expert'] = hcp_data['emerging_expert'].iloc[0]
        features['specialty'] = hcp_data['primary_phyn_spcl_desc'].iloc[0] if 'primary_phyn_spcl_desc' in hcp_data.columns else None

        # Publications features
        features['publications_count'] = hcp_data['publication_id'].dropna().nunique()
        features['first_author_count'] = len(hcp_data[hcp_data['authorship_position'] == 'First Author'])
        features['last_author_count'] = len(hcp_data[hcp_data['authorship_position'] == 'Last Author'])
        features['coauthor_count'] = len(hcp_data[hcp_data['authorship_position'] == 'Coauthor'])

        # Clinical trials features
        features['clinical_trials_count'] = hcp_data['ct_name'].dropna().nunique()
        features['pi_roles_count'] = len(hcp_data[hcp_data['ct_role'] == 'Principal Investigator'])
        features['investigator_roles_count'] = len(hcp_data[hcp_data['ct_role'] == 'Investigator'])

        # Conference features
        features['conferences_count'] = hcp_data['event_name'].dropna().nunique()
        features['conference_presentations_count'] = len(hcp_data[hcp_data['event_name'].notna()])

        # Time-based features
        try:
            if 'enumeration_year' in hcp_data.columns and hcp_data['enumeration_year'].notna().any():
                enum_year = int(hcp_data['enumeration_year'].iloc[0])
                features['years_since_enumeration'] = 2023 - enum_year
                features['enumeration_year'] = enum_year
            else:
                features['years_since_enumeration'] = np.nan
                features['enumeration_year'] = np.nan
        except (ValueError, TypeError):
            features['years_since_enumeration'] = np.nan
            features['enumeration_year'] = np.nan

        # Publication years analysis
        pub_years = hcp_data['pub_year'].dropna()
        if not pub_years.empty:
            features['first_publication_year'] = pub_years.min()
            features['latest_publication_year'] = pub_years.max()
            features['publication_span_years'] = pub_years.max() - pub_years.min()
            features['recent_publications_count'] = len(pub_years[pub_years >= 2020])
        else:
            features['first_publication_year'] = np.nan
            features['latest_publication_year'] = np.nan
            features['publication_span_years'] = np.nan
            features['recent_publications_count'] = 0

        # Conference years analysis
        conf_years = hcp_data['conf_year'].dropna()
        if not conf_years.empty:
            features['first_conference_year'] = conf_years.min()
            features['latest_conference_year'] = conf_years.max()
            features['conference_span_years'] = conf_years.max() - conf_years.min()
            features['recent_conferences_count'] = len(conf_years[conf_years >= 2020])
        else:
            features['first_conference_year'] = np.nan
            features['latest_conference_year'] = np.nan
            features['conference_span_years'] = np.nan
            features['recent_conferences_count'] = 0

        # Derived features
        features['total_activities'] = (features['publications_count'] +
                                      features['clinical_trials_count'] +
                                      features['conferences_count'])

        features['leadership_roles'] = features['pi_roles_count'] + features['first_author_count']

        # Activity ratios (avoid division by zero)
        if features['total_activities'] > 0:
            features['publications_ratio'] = features['publications_count'] / features['total_activities']
            features['trials_ratio'] = features['clinical_trials_count'] / features['total_activities']
            features['conferences_ratio'] = features['conferences_count'] / features['total_activities']
        else:
            features['publications_ratio'] = 0
            features['trials_ratio'] = 0
            features['conferences_ratio'] = 0

        return features

    @log_function_call()
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Handle missing values in the feature dataset.

        Args:
            df: Input DataFrame with features

        Returns:
            DataFrame with handled missing values
        """
        self.logger.info("Handling missing values")

        df_processed = df.copy()

        # Get missing value handling configuration
        missing_config = self.fe_config.get('missing_values', {})
        numerical_strategy = missing_config.get('numerical_strategy', 'median')
        categorical_strategy = missing_config.get('categorical_strategy', 'mode')
        constant_value = missing_config.get('constant_value', 0)

        # Get feature lists
        numerical_features = self.features_config.get('numerical', [])
        categorical_features = self.features_config.get('categorical', [])

        # Handle numerical features
        for feature in numerical_features:
            if feature in df_processed.columns and df_processed[feature].isnull().sum() > 0:
                if numerical_strategy == 'median':
                    fill_value = df_processed[feature].median()
                elif numerical_strategy == 'mean':
                    fill_value = df_processed[feature].mean()
                elif numerical_strategy == 'mode':
                    fill_value = df_processed[feature].mode()[0] if not df_processed[feature].mode().empty else 0
                elif numerical_strategy == 'constant':
                    fill_value = constant_value
                else:
                    fill_value = 0

                df_processed[feature].fillna(fill_value, inplace=True)
                self.logger.info(f"Filled missing values in {feature} with {numerical_strategy}: {fill_value}")

        # Handle categorical features
        for feature in categorical_features:
            if feature in df_processed.columns and df_processed[feature].isnull().sum() > 0:
                if categorical_strategy == 'mode':
                    fill_value = df_processed[feature].mode()[0] if not df_processed[feature].mode().empty else 'Unknown'
                elif categorical_strategy == 'constant':
                    fill_value = 'Unknown'
                else:
                    fill_value = 'Unknown'

                df_processed[feature].fillna(fill_value, inplace=True)
                self.logger.info(f"Filled missing values in {feature} with {categorical_strategy}: {fill_value}")

        # Log remaining missing values
        remaining_missing = df_processed.isnull().sum()
        if remaining_missing.sum() > 0:
            self.logger.warning(f"Remaining missing values: {dict(remaining_missing[remaining_missing > 0])}")

        return df_processed

    @log_function_call()
    def scale_features(self, df: pd.DataFrame, fit_scaler: bool = True) -> pd.DataFrame:
        """
        Scale numerical features.

        Args:
            df: Input DataFrame
            fit_scaler: Whether to fit the scaler (True for training, False for inference)

        Returns:
            DataFrame with scaled features
        """
        self.logger.info("Scaling numerical features")

        scaling_config = self.fe_config.get('scaling', {})
        scaling_method = scaling_config.get('method', 'standard')

        # Get numerical features to scale
        numerical_features = self.features_config.get('numerical', [])
        features_to_scale = [f for f in numerical_features if f in df.columns]

        if not features_to_scale:
            self.logger.warning("No numerical features found to scale")
            return df

        df_scaled = df.copy()

        # Initialize scaler if needed
        if self.scaler is None or fit_scaler:
            if scaling_method == 'standard':
                self.scaler = StandardScaler()
            elif scaling_method == 'minmax':
                self.scaler = MinMaxScaler()
            elif scaling_method == 'robust':
                self.scaler = RobustScaler()
            elif scaling_method == 'none':
                self.logger.info("No scaling applied")
                return df_scaled
            else:
                self.logger.warning(f"Unknown scaling method: {scaling_method}. Using standard scaling.")
                self.scaler = StandardScaler()

        # Apply scaling
        if fit_scaler:
            scaled_values = self.scaler.fit_transform(df_scaled[features_to_scale])
            self.logger.info(f"Fitted and transformed {len(features_to_scale)} features using {scaling_method} scaling")
        else:
            scaled_values = self.scaler.transform(df_scaled[features_to_scale])
            self.logger.info(f"Transformed {len(features_to_scale)} features using existing scaler")

        # Update DataFrame with scaled values
        df_scaled[features_to_scale] = scaled_values

        return df_scaled

    @log_function_call()
    def create_feature_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Create comprehensive feature statistics.

        Args:
            df: Input DataFrame

        Returns:
            Dictionary containing feature statistics
        """
        self.logger.info("Creating feature statistics")

        stats = {}

        # Overall statistics
        stats['overview'] = {
            'n_features': len(df.columns),
            'n_samples': len(df),
            'feature_names': list(df.columns)
        }

        # Numerical features statistics
        numerical_features = self.features_config.get('numerical', [])
        numerical_features = [f for f in numerical_features if f in df.columns]

        if numerical_features:
            stats['numerical'] = {}
            for feature in numerical_features:
                feature_stats = df[feature].describe().to_dict()
                feature_stats['missing_count'] = df[feature].isnull().sum()
                feature_stats['missing_percentage'] = (df[feature].isnull().sum() / len(df)) * 100
                stats['numerical'][feature] = feature_stats

        # Categorical features statistics
        categorical_features = self.features_config.get('categorical', [])
        categorical_features = [f for f in categorical_features if f in df.columns]

        if categorical_features:
            stats['categorical'] = {}
            for feature in categorical_features:
                value_counts = df[feature].value_counts().to_dict()
                stats['categorical'][feature] = {
                    'unique_count': df[feature].nunique(),
                    'value_counts': value_counts,
                    'missing_count': df[feature].isnull().sum(),
                    'missing_percentage': (df[feature].isnull().sum() / len(df)) * 100
                }

        # Class distribution by target variable
        if 'emerging_expert' in df.columns:
            stats['target_distribution'] = df['emerging_expert'].value_counts().to_dict()

        return stats

    @log_function_call()
    def save_processed_features(self, df: pd.DataFrame, filename: str) -> str:
        """
        Save processed features to file.

        Args:
            df: Processed features DataFrame
            filename: Output filename

        Returns:
            Path to saved file
        """
        try:
            output_path = self.config_manager.get_file_path('feature_engineering', filename.replace('.csv', ''))
        except ValueError:
            # Fallback: create path manually if not in config
            processed_data_dir = self.config_manager.get_config('paths').get('processed_data_dir', 'data/processed')
            output_path = str(Path(processed_data_dir) / f"{filename}.csv")
            self.logger.warning(f"File key not found in config, using fallback path: {output_path}")

        try:
            # Ensure directory exists
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            df.to_csv(output_path, index=False)
            self.logger.info(f"Processed features saved to: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"Failed to save processed features: {str(e)}")
            raise

    @log_function_call()
    def save_feature_statistics(self, stats: Dict[str, Any]) -> str:
        """
        Save feature statistics to JSON file.

        Args:
            stats: Feature statistics dictionary

        Returns:
            Path to saved file
        """
        try:
            output_path = self.config_manager.get_file_path('feature_engineering', 'feature_stats').replace('.csv', '.json')
        except ValueError:
            # Fallback: create path manually if not in config
            processed_data_dir = self.config_manager.get_config('paths').get('processed_data_dir', 'data/processed')
            output_path = str(Path(processed_data_dir) / 'feature_statistics.json')
            self.logger.warning(f"File key not found in config, using fallback path: {output_path}")

        try:
            # Ensure directory exists
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w') as f:
                json.dump(stats, f, indent=2, default=str)
            self.logger.info(f"Feature statistics saved to: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"Failed to save feature statistics: {str(e)}")
            raise

    def run_feature_engineering_pipeline(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Run the complete feature engineering pipeline.

        Args:
            df: Raw data DataFrame

        Returns:
            Tuple of (processed features DataFrame, feature statistics)
        """
        self.logger.info("Starting feature engineering pipeline")

        # Aggregate features at HCP level
        aggregated_df = self.aggregate_hcp_features(df)

        # Handle missing values
        processed_df = self.handle_missing_values(aggregated_df)

        # Scale features
        scaled_df = self.scale_features(processed_df, fit_scaler=True)

        # Create feature statistics
        feature_stats = self.create_feature_statistics(processed_df)

        # Save processed features
        self.save_processed_features(processed_df, 'aggregated_features')

        # Save feature statistics
        self.save_feature_statistics(feature_stats)

        self.logger.info("Feature engineering pipeline completed successfully")

        return processed_df, feature_stats


# Utility functions
def run_feature_engineering_stage(config_manager: ConfigManager, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Run the feature engineering stage of the pipeline.

    Args:
        config_manager: Configuration manager instance
        df: Input DataFrame

    Returns:
        Tuple of (processed features DataFrame, feature statistics)
    """
    processor = FeatureProcessor(config_manager)
    return processor.run_feature_engineering_pipeline(df)


# Example usage and testing
if __name__ == "__main__":
    from ..utils.config_manager import ConfigManager
    import pandas as pd

    # Test feature engineering functionality
    try:
        config_manager = ConfigManager("config/pipeline_config.yaml")

        # Create sample data for testing
        np.random.seed(42)
        sample_data = pd.DataFrame({
            'mdm_id': [f'HCP_{i//5}' for i in range(1000)],  # 5 rows per HCP
            'emerging_expert': np.random.choice(['Y', 'N'], 1000, p=[0.2, 0.8]),
            'publication_id': [f'PUB_{i}' if np.random.random() > 0.3 else None for i in range(1000)],
            'authorship_position': np.random.choice(['First Author', 'Last Author', 'Coauthor'], 1000),
            'ct_name': [f'CT_{i}' if np.random.random() > 0.7 else None for i in range(1000)],
            'ct_role': np.random.choice(['Principal Investigator', 'Investigator'], 1000),
            'event_name': [f'CONF_{i}' if np.random.random() > 0.5 else None for i in range(1000)],
            'enumeration_year': np.random.randint(2000, 2020, 1000),
            'pub_year': np.random.randint(2015, 2023, 1000),
            'conf_year': np.random.randint(2015, 2023, 1000),
            'primary_phyn_spcl_desc': np.random.choice(['Cardiology', 'Oncology', 'Neurology'], 1000)
        })

        if config_manager.is_stage_enabled('feature_engineering'):
            processed_df, stats = run_feature_engineering_stage(config_manager, sample_data)
            print("Feature engineering completed successfully!")
            print(f"Processed data shape: {processed_df.shape}")
            print(f"Number of features: {stats['overview']['n_features']}")
        else:
            print("Feature engineering stage is disabled in configuration")

    except Exception as e:
        print(f"Feature engineering test failed: {e}")
