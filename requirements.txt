# Healthcare ML Pipeline - Production Dependencies

# Core data science libraries
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Machine learning libraries
scikit-learn>=1.1.0
joblib>=1.2.0

# Visualization libraries
matplotlib>=3.5.0
seaborn>=0.11.0

# AWS libraries
boto3>=1.24.0
botocore>=1.27.0
PyAthena[pandas]>=2.25.0  # Primary AWS Athena data extraction (no Ray dependency)

# Configuration and utilities
PyYAML>=6.0
python-dateutil>=2.8.0
typing-extensions>=4.3.0

# Progress tracking and utilities
tqdm>=4.64.0

# Statistical analysis
statsmodels>=0.13.0

# Model interpretability
shap>=0.41.0

# Hyperparameter tuning
optuna>=3.0.0

# Performance monitoring
psutil>=5.9.0

# File handling
openpyxl>=3.0.0

# Network utilities
urllib3>=1.26.0
requests>=2.28.0

# Environment management
python-dotenv>=0.20.0

# Configuration validation
jsonschema>=4.0.0

# CLI enhancements
click>=8.1.0

# Development and testing (optional)
pytest>=7.0.0
jupyter>=1.0.0
ipykernel>=6.15.0

# Advanced visualization (optional)
plotly>=5.10.0

# Model monitoring (optional)
mlflow>=1.28.0

# Data profiling (optional)
ydata-profiling>=4.0.0
