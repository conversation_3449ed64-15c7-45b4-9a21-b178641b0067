# Streamlit Healthcare ML Pipeline - Frontend Dependencies

# Streamlit framework
streamlit>=1.28.0

# Core data science libraries (from main pipeline)
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Machine learning libraries (for model loading and prediction)
scikit-learn>=1.1.0
joblib>=1.2.0

# Visualization libraries for Streamlit
plotly>=5.10.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Configuration and utilities
PyYAML>=6.0
python-dateutil>=2.8.0

# Additional Streamlit components (optional)
streamlit-option-menu>=0.3.0
streamlit-aggrid>=0.3.0
streamlit-plotly-events>=0.0.6

# Performance and caching
cachetools>=5.0.0
