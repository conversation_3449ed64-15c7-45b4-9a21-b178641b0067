"""
Prediction Display Components
UI components for displaying prediction results and explanations.
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from typing import Dict, Any, List
import numpy as np


class PredictionDisplay:
    """
    Handles display of prediction results and model explanations.
    """

    def __init__(self):
        """Initialize the prediction display."""
        pass

    def render_prediction_results(self, prediction_result: Dict[str, Any], 
                                input_data: Dict[str, Any]) -> None:
        """
        Render the main prediction results.
        
        Args:
            prediction_result: Results from model prediction
            input_data: Original user input data
        """
        st.header("🔮 Prediction Results")
        
        # Main prediction display
        prediction_label = prediction_result['prediction_label']
        confidence = prediction_result['confidence']
        prob_positive = prediction_result['probability_positive']
        
        # Color coding based on prediction
        if prediction_result['prediction'] == 1:
            color = "green"
            icon = "🌟"
        else:
            color = "red" 
            icon = "📊"
            
        # Main result card
        st.markdown(f"""
        <div style="
            padding: 20px; 
            border-radius: 10px; 
            border: 2px solid {color}; 
            background-color: {'#e8f5e8' if color == 'green' else '#ffeaea'};
            text-align: center;
            margin: 20px 0;
        ">
            <h2 style="color: {color}; margin: 0;">{icon} {prediction_label}</h2>
            <h3 style="color: {color}; margin: 10px 0;">Confidence: {confidence:.1%}</h3>
            <p style="margin: 0;">Probability of being an Emerging Expert: <strong>{prob_positive:.1%}</strong></p>
        </div>
        """, unsafe_allow_html=True)

    def render_probability_visualization(self, prediction_result: Dict[str, Any]) -> None:
        """
        Render probability visualization.
        
        Args:
            prediction_result: Results from model prediction
        """
        st.subheader("📊 Probability Breakdown")
        
        prob_positive = prediction_result['probability_positive']
        prob_negative = prediction_result['probability_negative']
        
        # Create gauge chart
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = prob_positive * 100,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Emerging Expert Probability (%)"},
            delta = {'reference': 50},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 25], 'color': "lightgray"},
                    {'range': [25, 50], 'color': "yellow"},
                    {'range': [50, 75], 'color': "orange"},
                    {'range': [75, 100], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
        
        # Probability bars
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric(
                "Emerging Expert", 
                f"{prob_positive:.1%}",
                delta=f"{(prob_positive - 0.5):.1%}" if prob_positive != 0.5 else None
            )
            
        with col2:
            st.metric(
                "Not Emerging Expert",
                f"{prob_negative:.1%}",
                delta=f"{(prob_negative - 0.5):.1%}" if prob_negative != 0.5 else None
            )

    def render_feature_importance(self, prediction_result: Dict[str, Any], 
                                input_data: Dict[str, Any]) -> None:
        """
        Render feature importance analysis.
        
        Args:
            prediction_result: Results from model prediction
            input_data: Original user input data
        """
        st.subheader("🎯 Feature Importance Analysis")
        
        feature_importance = prediction_result.get('feature_importance', {})
        
        if feature_importance:
            # Create feature importance dataframe
            importance_df = pd.DataFrame([
                {'Feature': k.replace('_', ' ').title(), 
                 'Importance': v,
                 'Your Value': input_data.get(k, 0)}
                for k, v in feature_importance.items()
            ]).sort_values('Importance', ascending=True)
            
            # Create horizontal bar chart
            fig = px.bar(
                importance_df, 
                x='Importance', 
                y='Feature',
                orientation='h',
                title="Feature Importance in Model Decision",
                color='Importance',
                color_continuous_scale='viridis'
            )
            
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
            
            # Feature values table
            st.write("**Your Feature Values vs. Model Importance:**")
            st.dataframe(
                importance_df[['Feature', 'Your Value', 'Importance']].round(3),
                use_container_width=True
            )

    def render_model_explanation(self, prediction_result: Dict[str, Any],
                               input_data: Dict[str, Any]) -> None:
        """
        Render detailed model explanation.
        
        Args:
            prediction_result: Results from model prediction
            input_data: Original user input data
        """
        st.subheader("🧠 Model Explanation")
        
        # Get explanation text
        explanation = prediction_result.get('explanation', '')
        if explanation:
            st.markdown(explanation)
        
        # Model performance info
        model_performance = prediction_result.get('model_performance', {})
        if model_performance:
            st.write("**Model Performance Metrics:**")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                accuracy = model_performance.get('test_accuracy', 0)
                st.metric("Accuracy", f"{accuracy:.1%}")
                
            with col2:
                roc_auc = model_performance.get('roc_auc', 0)
                st.metric("ROC-AUC", f"{roc_auc:.3f}")
                
            with col3:
                f1_score = model_performance.get('classification_report', {}).get('weighted avg', {}).get('f1-score', 0)
                st.metric("F1-Score", f"{f1_score:.3f}")

    def render_comparison_with_peers(self, input_data: Dict[str, Any]) -> None:
        """
        Render comparison with peer statistics.
        
        Args:
            input_data: User input data
        """
        st.subheader("📈 Comparison with Peers")
        
        # This would ideally use actual peer statistics from the training data
        # For now, we'll use approximate values based on the feature statistics
        peer_stats = {
            "publications_count": {"median": 1, "75th_percentile": 4, "90th_percentile": 10},
            "clinical_trials_count": {"median": 0, "75th_percentile": 0, "90th_percentile": 1},
            "conferences_count": {"median": 0, "75th_percentile": 1, "90th_percentile": 3},
            "years_since_enumeration": {"median": 4, "75th_percentile": 6, "90th_percentile": 9}
        }
        
        comparison_data = []
        for feature, stats in peer_stats.items():
            user_value = input_data.get(feature, 0)
            if user_value is not None:
                comparison_data.append({
                    'Feature': feature.replace('_', ' ').title(),
                    'Your Value': user_value,
                    'Peer Median': stats['median'],
                    '75th Percentile': stats['75th_percentile'],
                    '90th Percentile': stats['90th_percentile']
                })
        
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            st.dataframe(comparison_df, use_container_width=True)
            
            # Create comparison chart
            fig = go.Figure()
            
            features = comparison_df['Feature'].tolist()
            
            fig.add_trace(go.Scatter(
                x=features,
                y=comparison_df['Your Value'],
                mode='markers+lines',
                name='Your Values',
                marker=dict(size=10, color='blue'),
                line=dict(width=3)
            ))
            
            fig.add_trace(go.Scatter(
                x=features,
                y=comparison_df['Peer Median'],
                mode='markers+lines',
                name='Peer Median',
                marker=dict(size=8, color='orange'),
                line=dict(width=2, dash='dash')
            ))
            
            fig.add_trace(go.Scatter(
                x=features,
                y=comparison_df['90th Percentile'],
                mode='markers+lines',
                name='90th Percentile',
                marker=dict(size=8, color='green'),
                line=dict(width=2, dash='dot')
            ))
            
            fig.update_layout(
                title="Your Profile vs. Peer Benchmarks",
                xaxis_title="Features",
                yaxis_title="Values",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)

    def render_recommendations(self, prediction_result: Dict[str, Any],
                             input_data: Dict[str, Any]) -> None:
        """
        Render personalized recommendations.
        
        Args:
            prediction_result: Results from model prediction
            input_data: Original user input data
        """
        st.subheader("💡 Personalized Recommendations")
        
        recommendations = []
        
        # Publications recommendations
        pub_count = input_data.get('publications_count', 0)
        first_author = input_data.get('first_author_count', 0)
        
        if pub_count < 5:
            recommendations.append("📚 **Increase Publication Output**: Aim for 5+ publications to strengthen your profile")
        
        if first_author == 0 and pub_count > 0:
            recommendations.append("✍️ **Lead Research Projects**: Consider leading research that results in first-author publications")
        elif first_author < pub_count * 0.3:
            recommendations.append("🎯 **Increase First-Author Publications**: Aim for 30%+ of publications as first author")
            
        # Clinical trials recommendations
        ct_count = input_data.get('clinical_trials_count', 0)
        if ct_count == 0:
            recommendations.append("🔬 **Engage in Clinical Research**: Participate in clinical trials to demonstrate research involvement")
        elif input_data.get('pi_roles_count', 0) == 0 and ct_count > 0:
            recommendations.append("👨‍🔬 **Seek Leadership Roles**: Consider pursuing Principal Investigator opportunities")
            
        # Conference recommendations
        conf_count = input_data.get('conferences_count', 0)
        if conf_count < 3:
            recommendations.append("🎤 **Increase Conference Presence**: Present at professional conferences to build visibility")
            
        # Display recommendations
        if recommendations:
            for rec in recommendations:
                st.write(rec)
        else:
            st.write("🎉 **Excellent profile!** You're demonstrating strong activity across all key areas.")
