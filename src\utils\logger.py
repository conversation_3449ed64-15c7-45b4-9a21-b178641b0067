"""
Logging utilities for the Healthcare ML Pipeline.
Provides centralized logging configuration and utilities.
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any


class PipelineLogger:
    """
    Centralized logging system for the pipeline.
    Supports both console and file logging with configurable levels.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the logger with configuration.
        
        Args:
            config: Logging configuration dictionary
        """
        self.config = config
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Set up the logger with the specified configuration."""
        # Create logger
        self.logger = logging.getLogger('healthcare_pipeline')
        self.logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # Console handler
        if self.config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, self.config.get('level', 'INFO')))
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # File handler
        if self.config.get('file_output', True):
            log_dir = Path(self.config.get('log_dir', 'logs'))
            log_dir.mkdir(exist_ok=True)
            
            # Generate log filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = self.config.get('log_file', 'pipeline_{timestamp}.log')
            log_filename = log_filename.format(timestamp=timestamp)
            log_path = log_dir / log_filename
            
            file_handler = logging.FileHandler(log_path)
            file_handler.setLevel(getattr(logging, self.config.get('level', 'INFO')))
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
            
            self.logger.info(f"Log file created: {log_path}")
    
    def get_logger(self) -> logging.Logger:
        """Get the configured logger instance."""
        return self.logger
    
    def log_stage_start(self, stage_name: str):
        """Log the start of a pipeline stage."""
        separator = "=" * 80
        self.logger.info(f"\n{separator}")
        self.logger.info(f"STARTING STAGE: {stage_name.upper()}")
        self.logger.info(f"{separator}")
    
    def log_stage_end(self, stage_name: str, success: bool = True):
        """Log the end of a pipeline stage."""
        status = "COMPLETED" if success else "FAILED"
        separator = "=" * 80
        self.logger.info(f"{separator}")
        self.logger.info(f"STAGE {status}: {stage_name.upper()}")
        self.logger.info(f"{separator}\n")
    
    def log_data_info(self, data_name: str, shape: tuple, missing_values: Optional[int] = None):
        """Log information about a dataset."""
        self.logger.info(f"Dataset: {data_name}")
        self.logger.info(f"  Shape: {shape}")
        if missing_values is not None:
            self.logger.info(f"  Missing values: {missing_values}")
    
    def log_model_performance(self, model_name: str, metrics: Dict[str, float]):
        """Log model performance metrics."""
        self.logger.info(f"Model Performance - {model_name}:")
        for metric, value in metrics.items():
            self.logger.info(f"  {metric}: {value:.4f}")
    
    def log_config_info(self, config_section: str, config_data: Dict[str, Any]):
        """Log configuration information."""
        self.logger.info(f"Configuration - {config_section}:")
        for key, value in config_data.items():
            self.logger.info(f"  {key}: {value}")
    
    def log_file_operation(self, operation: str, file_path: str, success: bool = True):
        """Log file operations (save, load, etc.)."""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"File {operation} [{status}]: {file_path}")
    
    def log_error(self, error_msg: str, exception: Optional[Exception] = None):
        """Log error messages with optional exception details."""
        self.logger.error(f"ERROR: {error_msg}")
        if exception:
            self.logger.error(f"Exception details: {str(exception)}")
    
    def log_warning(self, warning_msg: str):
        """Log warning messages."""
        self.logger.warning(f"WARNING: {warning_msg}")
    
    def log_progress(self, current: int, total: int, operation: str = "Processing"):
        """Log progress information."""
        percentage = (current / total) * 100
        self.logger.info(f"{operation}: {current}/{total} ({percentage:.1f}%)")


class StageLogger:
    """
    Context manager for logging pipeline stages.
    Automatically logs stage start and end with error handling.
    """
    
    def __init__(self, logger: PipelineLogger, stage_name: str):
        """
        Initialize the stage logger.
        
        Args:
            logger: PipelineLogger instance
            stage_name: Name of the pipeline stage
        """
        self.logger = logger
        self.stage_name = stage_name
        self.success = False
    
    def __enter__(self):
        """Enter the context and log stage start."""
        self.logger.log_stage_start(self.stage_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context and log stage end."""
        if exc_type is None:
            self.success = True
        else:
            self.logger.log_error(
                f"Stage {self.stage_name} failed",
                exc_val
            )
        
        self.logger.log_stage_end(self.stage_name, self.success)
        return False  # Don't suppress exceptions


def setup_logging(config: Dict[str, Any]) -> PipelineLogger:
    """
    Set up logging for the pipeline.
    
    Args:
        config: Logging configuration dictionary
    
    Returns:
        Configured PipelineLogger instance
    """
    return PipelineLogger(config)


def get_logger(name: str = 'healthcare_pipeline') -> logging.Logger:
    """
    Get a logger instance by name.
    
    Args:
        name: Logger name
    
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


# Decorator for automatic function logging
def log_function_call(logger: Optional[logging.Logger] = None):
    """
    Decorator to automatically log function calls.
    
    Args:
        logger: Logger instance (optional)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_logger = logger or get_logger()
            func_logger.debug(f"Calling function: {func.__name__}")
            try:
                result = func(*args, **kwargs)
                func_logger.debug(f"Function {func.__name__} completed successfully")
                return result
            except Exception as e:
                func_logger.error(f"Function {func.__name__} failed: {str(e)}")
                raise
        return wrapper
    return decorator


# Example usage and testing
if __name__ == "__main__":
    # Test configuration
    test_config = {
        'level': 'INFO',
        'log_dir': 'logs',
        'log_file': 'test_pipeline_{timestamp}.log',
        'console_output': True,
        'file_output': True
    }
    
    # Create logger
    pipeline_logger = setup_logging(test_config)
    
    # Test logging functionality
    with StageLogger(pipeline_logger, "test_stage"):
        pipeline_logger.get_logger().info("This is a test log message")
        pipeline_logger.log_data_info("test_data", (100, 10), 5)
        pipeline_logger.log_model_performance("test_model", {"accuracy": 0.85, "f1": 0.82})
        pipeline_logger.log_file_operation("save", "test_file.csv")
    
    print("Logging test completed successfully!")
