# Deployment Guide - Healthcare Emerging Expert Predictor

This guide provides step-by-step instructions for deploying the Streamlit application.

## 🚀 Local Development Setup

### Prerequisites
- Python 3.8+ installed and accessible
- Completed ML pipeline with trained models
- Git (for version control)

### Step 1: Verify ML Pipeline
```bash
# Ensure you're in the main project directory
cd /path/to/Emg_Exp_ML_Pipeline

# Run the ML pipeline to ensure models are trained
python pipeline_orchestrator.py

# Verify required files exist
ls models/                          # Should contain .joblib model files
ls data/processed/                  # Should contain feature_statistics.json and modeling_results.json
```

### Step 2: Install Streamlit Dependencies
```bash
# Install Streamlit-specific requirements
pip install -r streamlit_app/requirements_streamlit.txt

# Or install individually if needed
pip install streamlit plotly
```

### Step 3: Test Integration
```bash
# Run integration tests
python test_streamlit_integration.py

# Should show all tests passing
```

### Step 4: Launch Application
```bash
# Option 1: Use the launcher script
python run_streamlit.py

# Option 2: Run directly
cd streamlit_app
streamlit run app.py

# Option 3: Specify port
streamlit run app.py --server.port 8502
```

### Step 5: Access Application
- Open browser to `http://localhost:8501`
- The application should load with the input form
- Test with example profiles to verify functionality

## 🌐 Production Deployment

### Option 1: Streamlit Cloud (Recommended)

1. **Prepare Repository**
```bash
# Ensure all files are committed
git add .
git commit -m "Add Streamlit application"
git push origin main
```

2. **Deploy to Streamlit Cloud**
- Go to [share.streamlit.io](https://share.streamlit.io)
- Connect your GitHub repository
- Set main file path: `streamlit_app/app.py`
- Set Python version: 3.8+
- Deploy

3. **Configure Secrets** (if needed)
- Add any API keys or sensitive configuration
- Use Streamlit Cloud secrets management

### Option 2: Docker Deployment

1. **Create Dockerfile**
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements_streamlit.txt .
RUN pip install -r requirements_streamlit.txt

# Copy application files
COPY . .

# Expose port
EXPOSE 8501

# Run Streamlit
CMD ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

2. **Build and Run**
```bash
# Build Docker image
docker build -t healthcare-ml-app .

# Run container
docker run -p 8501:8501 healthcare-ml-app
```

### Option 3: Cloud Platform Deployment

#### AWS EC2
```bash
# Launch EC2 instance
# Install Python and dependencies
# Clone repository
# Run Streamlit with public access
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

#### Google Cloud Platform
```bash
# Use Google Cloud Run or Compute Engine
# Configure firewall rules for port 8501
# Deploy using Cloud Build or manual setup
```

#### Azure
```bash
# Use Azure Container Instances or App Service
# Configure networking and security groups
# Deploy using Azure CLI or portal
```

## 🔧 Configuration Options

### Environment Variables
```bash
# Set custom model directory
export MODELS_DIR="/path/to/models"

# Set custom data directory  
export DATA_DIR="/path/to/data"

# Set logging level
export LOG_LEVEL="INFO"
```

### Streamlit Configuration
Edit `streamlit_app/.streamlit/config.toml`:
```toml
[server]
port = 8501
enableCORS = false

[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
```

## 🔒 Security Considerations

### Data Privacy
- No user data is stored permanently
- All processing happens in memory
- Consider adding data encryption for sensitive deployments

### Access Control
- Add authentication if needed:
```python
# Add to app.py
import streamlit_authenticator as stauth

# Configure authentication
authenticator = stauth.Authenticate(...)
name, authentication_status, username = authenticator.login('Login', 'main')
```

### Network Security
- Use HTTPS in production
- Configure firewall rules appropriately
- Consider VPN access for internal deployments

## 📊 Monitoring and Maintenance

### Application Monitoring
```python
# Add to app.py for usage tracking
import streamlit as st

# Track usage
if 'prediction_count' not in st.session_state:
    st.session_state.prediction_count = 0

# Increment on prediction
st.session_state.prediction_count += 1
```

### Model Updates
1. Retrain models using main pipeline
2. Replace model files in `models/` directory
3. Update feature statistics if schema changes
4. Restart Streamlit application

### Performance Optimization
- Enable Streamlit caching for models
- Use session state for user data
- Optimize feature processing for single predictions
- Consider model quantization for faster inference

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**
```bash
# Check Python path
python -c "import sys; print(sys.path)"

# Install missing dependencies
pip install -r requirements_streamlit.txt
```

2. **Model Loading Errors**
```bash
# Verify model files exist
ls -la models/

# Check file permissions
chmod 644 models/*.joblib
```

3. **Feature Statistics Missing**
```bash
# Regenerate feature statistics
python pipeline_orchestrator.py --stages feature_engineering
```

4. **Port Already in Use**
```bash
# Use different port
streamlit run app.py --server.port 8502

# Or kill existing process
lsof -ti:8501 | xargs kill -9
```

### Performance Issues
- Check memory usage with large models
- Enable caching for better performance
- Consider model optimization techniques
- Monitor CPU usage during predictions

## 📈 Scaling Considerations

### High Traffic
- Use load balancer for multiple instances
- Consider model serving infrastructure (e.g., TensorFlow Serving)
- Implement request queuing for heavy loads
- Cache predictions for common inputs

### Multiple Models
- Implement model versioning
- A/B testing framework for model comparison
- Gradual rollout of new models
- Performance monitoring and alerting

## 🤝 Support

For issues with the Streamlit application:
1. Check this deployment guide
2. Review application logs
3. Test with the integration test script
4. Verify ML pipeline is working correctly
