# Streamlit Application Configuration

app:
  name: "Healthcare Emerging Expert Predictor"
  version: "1.0.0"
  description: "ML-powered prediction tool for identifying emerging healthcare experts"
  
# UI Configuration
ui:
  theme: "light"
  layout: "wide"
  sidebar_state: "expanded"
  
  # Color scheme
  colors:
    primary: "#1f77b4"
    secondary: "#ff7f0e" 
    success: "#2ca02c"
    warning: "#ff7f0e"
    error: "#d62728"
    
  # Layout settings
  layout:
    max_width: 1200
    padding: "1rem"
    
# Model Configuration
models:
  default_model: "gradient_boosting"
  models_directory: "../models"
  results_file: "../data/processed/modeling_results.json"
  feature_stats_file: "../data/processed/feature_statistics.json"
  
# Input Validation
validation:
  strict_mode: false
  allow_missing_optional: true
  
# Features Configuration
features:
  required_features:
    - "publications_count"
    - "first_author_count"
    - "clinical_trials_count"
    - "pi_roles_count"
    - "conferences_count"
    - "specialty"
    
  optional_features:
    - "years_since_enumeration"
    - "enumeration_year"
    - "first_publication_year"
    - "latest_publication_year"
    - "recent_publications_count"
    - "first_conference_year"
    - "latest_conference_year"
    - "recent_conferences_count"

# Display Configuration
display:
  show_model_performance: true
  show_feature_importance: true
  show_peer_comparison: true
  show_recommendations: true
  show_what_if_analysis: true
  show_methodology: true
  
  # Probability display
  probability:
    show_gauge: true
    show_bars: true
    decimal_places: 1
    
  # Charts configuration
  charts:
    height: 400
    color_scheme: "viridis"
    
# Caching Configuration
caching:
  enable_model_caching: true
  enable_feature_caching: true
  cache_ttl: 3600  # 1 hour
  
# Logging Configuration
logging:
  level: "INFO"
  enable_streamlit_logging: true
