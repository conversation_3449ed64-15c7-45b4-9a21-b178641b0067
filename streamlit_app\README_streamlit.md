# Healthcare Emerging Expert Predictor - Streamlit Application

A user-friendly web interface for predicting whether healthcare professionals are likely to become emerging experts in their field.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Trained ML models (from the main pipeline)
- Required dependencies

### Installation

1. **Install Streamlit dependencies:**
```bash
cd streamlit_app
pip install -r requirements_streamlit.txt
```

2. **Ensure models are trained:**
```bash
# From the main project directory
python pipeline_orchestrator.py
```

3. **Run the Streamlit application:**
```bash
streamlit run app.py
```

4. **Open your browser** to `http://localhost:8501`

## 📋 Features

### 🎯 Core Functionality
- **Interactive Input Forms**: Tabbed interface for entering healthcare professional data
- **Real-time Validation**: Input validation with helpful error messages and suggestions
- **ML Prediction**: Uses the best-performing trained model (Gradient Boosting, 95.4% accuracy)
- **Probability Visualization**: Gauge charts and probability breakdowns
- **Feature Importance**: Shows which factors most influence the prediction
- **Peer Comparison**: Compare input profile against training data statistics

### 📊 Analysis Features
- **What-If Analysis**: Explore how changes to profile affect predictions
- **Personalized Recommendations**: Actionable suggestions for career development
- **Model Explanation**: Detailed methodology and performance metrics
- **Example Profiles**: Pre-loaded examples for demonstration

### 🔧 Technical Features
- **Model Selection**: Choose between different trained models
- **Input Sanitization**: Automatic data cleaning and validation
- **Error Handling**: Robust error handling with user-friendly messages
- **Responsive Design**: Works on desktop and mobile devices

## 📝 Usage Guide

### Input Categories

#### 📚 Publications
- **Total Publications**: Number of peer-reviewed publications
- **First Author Publications**: Publications where you were primary author
- **Publication Timeline**: Years of first and latest publications

#### 🔬 Clinical Trials
- **Clinical Trials**: Number of trials participated in
- **PI Roles**: Number of trials where you were Principal Investigator

#### 🎤 Conferences
- **Conference Presentations**: Number of conferences presented at
- **Conference Timeline**: Years of first and latest presentations

#### 👨‍⚕️ Professional Info
- **Medical Specialty**: Primary field of practice
- **License Information**: Year licensed and years of experience

### Understanding Results

#### Probability Scores
- **0-25%**: Low likelihood of emerging expert status
- **25-50%**: Below average likelihood
- **50-75%**: Above average likelihood
- **75-100%**: High likelihood of emerging expert status

#### Feature Importance
- Shows which aspects of your profile most influenced the prediction
- Higher importance = greater impact on the result
- Use to understand key drivers of emerging expert recognition

## 🏗️ Architecture

### Directory Structure
```
streamlit_app/
├── app.py                          # Main Streamlit application
├── components/                     # UI components
│   ├── input_forms.py             # User input forms
│   ├── prediction_display.py      # Results visualization
│   └── model_explanation.py       # Model explanations
├── pipeline/                      # Pipeline integration
│   ├── feature_processor.py       # Feature engineering
│   ├── model_predictor.py         # Model loading & prediction
│   └── data_validator.py          # Input validation
├── config/                        # Configuration
│   └── app_config.yaml           # App settings
└── requirements_streamlit.txt     # Dependencies
```

### Integration with Main Pipeline
- **Feature Engineering**: Reuses exact same feature calculation logic
- **Model Loading**: Loads pre-trained models from main pipeline
- **Data Processing**: Applies same preprocessing steps as training
- **Validation**: Ensures input data quality and consistency

## 🔧 Configuration

### Model Configuration
- Default model: Gradient Boosting (best performance)
- Model directory: `../models/`
- Feature statistics: `../data/processed/feature_statistics.json`

### UI Configuration
- Layout: Wide mode for better visualization
- Theme: Light theme with professional color scheme
- Caching: Enabled for models and features (1-hour TTL)

## 🚨 Troubleshooting

### Common Issues

1. **Models not found**
   - Ensure the main ML pipeline has been run successfully
   - Check that model files exist in the `models/` directory

2. **Feature statistics missing**
   - Run the complete pipeline to generate feature statistics
   - Check `data/processed/feature_statistics.json` exists

3. **Import errors**
   - Ensure all dependencies are installed: `pip install -r requirements_streamlit.txt`
   - Check Python path configuration

4. **Prediction errors**
   - Validate input data format
   - Check model compatibility with feature format

### Performance Optimization
- Models are cached on first load
- Feature processing is optimized for single predictions
- UI components use Streamlit's built-in caching

## 📈 Model Performance

The application uses models trained on 5,675+ healthcare professionals:

- **Gradient Boosting**: 95.4% accuracy, 0.958 ROC-AUC (default)
- **Random Forest**: 94.3% accuracy, 0.944 ROC-AUC
- **Logistic Regression**: 92.2% accuracy, 0.965 ROC-AUC

## 🤝 Contributing

To extend the application:

1. **Add new features**: Modify `feature_processor.py`
2. **Enhance UI**: Update components in `components/`
3. **Add models**: Extend `model_predictor.py`
4. **Improve validation**: Update `data_validator.py`

## 📄 License

This application is part of the Healthcare ML Pipeline project.
