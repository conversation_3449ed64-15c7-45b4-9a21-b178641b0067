"""
Modeling module for the Healthcare ML Pipeline.
Handles label correction, model training, and prediction generation.
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
import joblib
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import logging
import json

from ..utils.logger import get_logger, log_function_call
from ..utils.config_manager import ConfigManager


class ModelTrainer:
    """
    Handles model training, label correction, and prediction generation.
    """

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the model trainer.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.modeling_config = config_manager.get_stage_config('modeling')
        self.features_config = config_manager.get_features_config()
        self.logger = get_logger(__name__)

        # Initialize models dictionary
        self.models = {}
        self.label_correction_model = None
        self.optimal_threshold = 0.5

    @log_function_call()
    def prepare_data_for_modeling(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Prepare data for model training.

        Args:
            df: Input DataFrame with features

        Returns:
            Tuple of (X, y, feature_names)
        """
        self.logger.info("Preparing data for modeling")

        # Get feature columns
        numerical_features = self.features_config.get('numerical', [])
        feature_columns = [f for f in numerical_features if f in df.columns]

        if not feature_columns:
            raise ValueError("No valid feature columns found for modeling")

        # Prepare features (X)
        X = df[feature_columns].copy()

        # Handle missing values
        imputer = SimpleImputer(strategy='median')
        X_imputed = imputer.fit_transform(X)

        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_imputed)

        # Prepare target (y)
        if 'emerging_expert' not in df.columns:
            raise ValueError("Target column 'emerging_expert' not found in DataFrame")

        y = (df['emerging_expert'] == 'Y').astype(int)

        self.logger.info(f"Data prepared: {X_scaled.shape[0]} samples, {X_scaled.shape[1]} features")
        self.logger.info(f"Class distribution: {np.bincount(y)}")

        return X_scaled, y, feature_columns

    @log_function_call()
    def train_label_correction_model(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """
        Train a model for label correction.

        Args:
            X: Feature matrix
            y: Target vector
            feature_names: List of feature names

        Returns:
            Dictionary containing model results
        """
        self.logger.info("Training label correction model")

        # Get label correction configuration
        lc_config = self.modeling_config.get('label_correction', {})

        # Split data
        training_config = self.modeling_config.get('training', {})
        test_size = training_config.get('test_size', 0.2)
        random_state = training_config.get('random_state', 42)
        stratify = training_config.get('stratify', True)

        X_train, X_test, y_train, y_test = train_test_split(
            X, y,
            test_size=test_size,
            random_state=random_state,
            stratify=y if stratify else None
        )

        # Initialize and train model
        model_type = lc_config.get('model_type', 'logistic_regression')

        if model_type == 'logistic_regression':
            self.label_correction_model = LogisticRegression(
                class_weight=lc_config.get('class_weight', 'balanced'),
                max_iter=lc_config.get('max_iter', 1000),
                random_state=lc_config.get('random_state', 42)
            )
        else:
            raise ValueError(f"Unsupported label correction model type: {model_type}")

        # Train the model
        self.label_correction_model.fit(X_train, y_train)

        # Generate predictions
        y_pred_proba = self.label_correction_model.predict_proba(X_test)[:, 1]
        y_pred = self.label_correction_model.predict(X_test)

        # Find optimal threshold
        self.optimal_threshold = self._find_optimal_threshold(y_test, y_pred_proba, lc_config.get('threshold_method', 'youden_j'))

        # Calculate metrics
        results = {
            'model_type': model_type,
            'optimal_threshold': self.optimal_threshold,
            'test_accuracy': (y_pred == y_test).mean(),
            'classification_report': classification_report(y_test, y_pred, output_dict=True),
            'feature_coefficients': dict(zip(feature_names, self.label_correction_model.coef_[0])) if hasattr(self.label_correction_model, 'coef_') else {}
        }

        # Calculate ROC AUC
        fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
        results['roc_auc'] = auc(fpr, tpr)

        self.logger.info(f"Label correction model trained. ROC AUC: {results['roc_auc']:.4f}")

        return results

    def _find_optimal_threshold(self, y_true: np.ndarray, y_proba: np.ndarray, method: str = 'youden_j') -> float:
        """
        Find optimal threshold for binary classification.

        Args:
            y_true: True labels
            y_proba: Predicted probabilities
            method: Method for finding optimal threshold

        Returns:
            Optimal threshold value
        """
        fpr, tpr, thresholds = roc_curve(y_true, y_proba)

        if method == 'youden_j':
            # Youden's J statistic: sensitivity + specificity - 1
            j_scores = tpr - fpr
            optimal_idx = np.argmax(j_scores)
            optimal_threshold = thresholds[optimal_idx]
        elif method == 'f1_optimal':
            # Find threshold that maximizes F1 score
            from sklearn.metrics import f1_score
            f1_scores = []
            for threshold in thresholds:
                y_pred_thresh = (y_proba >= threshold).astype(int)
                f1_scores.append(f1_score(y_true, y_pred_thresh))
            optimal_idx = np.argmax(f1_scores)
            optimal_threshold = thresholds[optimal_idx]
        else:
            # Default to 0.5
            optimal_threshold = 0.5

        return optimal_threshold

    @log_function_call()
    def identify_mislabeled_points(self, df: pd.DataFrame, X: np.ndarray) -> pd.DataFrame:
        """
        Identify potentially mislabeled data points.

        Args:
            df: Original DataFrame
            X: Feature matrix

        Returns:
            DataFrame with potentially mislabeled points
        """
        self.logger.info("Identifying potentially mislabeled data points")

        if self.label_correction_model is None:
            raise ValueError("Label correction model not trained yet")

        # Get predictions for all data
        all_proba = self.label_correction_model.predict_proba(X)[:, 1]

        # Add predictions to DataFrame
        df_with_pred = df.copy()
        df_with_pred['predicted_probability'] = all_proba

        # Identify potential mislabeled points (labeled as 'N' but high probability of being 'Y')
        potential_mislabeled = df_with_pred[
            (df_with_pred['emerging_expert'] == 'N') &
            (df_with_pred['predicted_probability'] >= self.optimal_threshold)
        ]

        self.logger.info(f"Identified {len(potential_mislabeled)} potentially mislabeled points")

        return potential_mislabeled

    @log_function_call()
    def correct_labels(self, df: pd.DataFrame, X: np.ndarray) -> pd.DataFrame:
        """
        Create corrected labels based on model predictions.

        Args:
            df: Original DataFrame
            X: Feature matrix

        Returns:
            DataFrame with corrected labels
        """
        self.logger.info("Creating corrected labels")

        if self.label_correction_model is None:
            raise ValueError("Label correction model not trained yet")

        # Get predictions for all data
        all_proba = self.label_correction_model.predict_proba(X)[:, 1]

        # Create corrected dataset
        df_corrected = df.copy()
        df_corrected['predicted_probability'] = all_proba
        df_corrected['corrected_label'] = df_corrected['emerging_expert']

        # Update labels for potentially mislabeled points
        mask = (df_corrected['emerging_expert'] == 'N') & (df_corrected['predicted_probability'] >= self.optimal_threshold)
        df_corrected.loc[mask, 'corrected_label'] = 'Y'

        # Log changes
        n_changed = mask.sum()
        total_n = len(df_corrected)
        self.logger.info(f"Corrected {n_changed} labels ({n_changed/total_n:.2%} of total)")

        return df_corrected

    @log_function_call()
    def train_multiple_models(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """
        Train multiple models for comparison.

        Args:
            X: Feature matrix
            y: Target vector
            feature_names: List of feature names

        Returns:
            Dictionary containing results for all models
        """
        self.logger.info("Training multiple models")

        # Get model configurations
        models_config = self.modeling_config.get('models', {})
        training_config = self.modeling_config.get('training', {})

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y,
            test_size=training_config.get('test_size', 0.2),
            random_state=training_config.get('random_state', 42),
            stratify=y if training_config.get('stratify', True) else None
        )

        results = {}

        for model_name, model_config in models_config.items():
            self.logger.info(f"Training {model_name}")

            try:
                # Initialize model
                model = self._initialize_model(model_name, model_config)

                # Train model
                model.fit(X_train, y_train)

                # Generate predictions
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None

                # Calculate metrics
                model_results = {
                    'model_config': model_config,
                    'test_accuracy': (y_pred == y_test).mean(),
                    'classification_report': classification_report(y_test, y_pred, output_dict=True)
                }

                # Add ROC AUC if probabilities available
                if y_pred_proba is not None:
                    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
                    model_results['roc_auc'] = auc(fpr, tpr)

                # Add feature importance if available
                if hasattr(model, 'feature_importances_'):
                    model_results['feature_importance'] = dict(zip(feature_names, model.feature_importances_))
                elif hasattr(model, 'coef_'):
                    model_results['feature_coefficients'] = dict(zip(feature_names, model.coef_[0]))

                # Perform cross-validation
                cv_folds = training_config.get('cross_validation_folds', 5)
                cv_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, scoring='f1')
                model_results['cv_f1_mean'] = cv_scores.mean()
                model_results['cv_f1_std'] = cv_scores.std()

                # Store model
                self.models[model_name] = model
                results[model_name] = model_results

                self.logger.info(f"{model_name} trained successfully. ROC AUC: {model_results.get('roc_auc', 'N/A')}")

            except Exception as e:
                self.logger.error(f"Failed to train {model_name}: {str(e)}")
                results[model_name] = {'error': str(e)}

        return results

    def _initialize_model(self, model_name: str, model_config: Dict[str, Any]):
        """
        Initialize a model based on configuration.

        Args:
            model_name: Name of the model
            model_config: Model configuration dictionary

        Returns:
            Initialized model instance
        """
        if model_name == 'logistic_regression':
            return LogisticRegression(**model_config)
        elif model_name == 'random_forest':
            return RandomForestClassifier(**model_config)
        elif model_name == 'gradient_boosting':
            return GradientBoostingClassifier(**model_config)
        else:
            raise ValueError(f"Unsupported model type: {model_name}")

    @log_function_call()
    def save_models(self) -> Dict[str, str]:
        """
        Save trained models to disk.

        Returns:
            Dictionary mapping model names to file paths
        """
        self.logger.info("Saving trained models")

        saved_paths = {}

        # Save label correction model
        if self.label_correction_model is not None:
            lc_path = self.config_manager.get_model_path('label_correction_model.joblib')
            joblib.dump(self.label_correction_model, lc_path)
            saved_paths['label_correction'] = lc_path
            self.logger.info(f"Label correction model saved to: {lc_path}")

        # Save other models
        for model_name, model in self.models.items():
            model_path = self.config_manager.get_model_path(f'{model_name}_model.joblib')
            joblib.dump(model, model_path)
            saved_paths[model_name] = model_path
            self.logger.info(f"{model_name} model saved to: {model_path}")

        return saved_paths

    @log_function_call()
    def save_results(self, results: Dict[str, Any], filename: str) -> str:
        """
        Save modeling results to JSON file.

        Args:
            results: Results dictionary
            filename: Output filename

        Returns:
            Path to saved file
        """
        try:
            # Try to get the configured file path
            output_path = self.config_manager.get_file_path('modeling', filename.replace('.json', '')).replace('.csv', '.json')
        except ValueError:
            # Fallback: create path manually if not in config
            processed_data_dir = self.config_manager.get_config('paths').get('processed_data_dir', 'data/processed')
            output_path = str(Path(processed_data_dir) / f"{filename}.json")
            self.logger.warning(f"File key not found in config, using fallback path: {output_path}")

        try:
            # Ensure directory exists
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            self.logger.info(f"Results saved to: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"Failed to save results: {str(e)}")
            raise

    def run_modeling_pipeline(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Run the complete modeling pipeline.

        Args:
            df: Input DataFrame with features

        Returns:
            Tuple of (corrected DataFrame, modeling results)
        """
        self.logger.info("Starting modeling pipeline")

        # Prepare data
        X, y, feature_names = self.prepare_data_for_modeling(df)

        # Train label correction model
        lc_results = self.train_label_correction_model(X, y, feature_names)

        # Identify mislabeled points
        mislabeled_df = self.identify_mislabeled_points(df, X)

        # Create corrected labels
        corrected_df = self.correct_labels(df, X)

        # Train multiple models on corrected data
        X_corrected, y_corrected, _ = self.prepare_data_for_modeling(corrected_df.assign(emerging_expert=corrected_df['corrected_label']))
        model_results = self.train_multiple_models(X_corrected, y_corrected, feature_names)

        # Combine all results
        all_results = {
            'label_correction': lc_results,
            'models': model_results,
            'mislabeled_count': len(mislabeled_df),
            'correction_summary': {
                'original_positive': (df['emerging_expert'] == 'Y').sum(),
                'corrected_positive': (corrected_df['corrected_label'] == 'Y').sum(),
                'labels_changed': (df['emerging_expert'] != corrected_df['corrected_label']).sum()
            }
        }

        # Save results and models
        self.save_results(all_results, 'modeling_results')
        self.save_models()

        # Save corrected data and mislabeled points with robust path handling
        try:
            corrected_path = self.config_manager.get_file_path('modeling', 'corrected_labels')
        except ValueError:
            processed_data_dir = self.config_manager.get_config('paths').get('processed_data_dir', 'data/processed')
            corrected_path = str(Path(processed_data_dir) / 'hcp_corrected_labels.csv')

        Path(corrected_path).parent.mkdir(parents=True, exist_ok=True)
        corrected_df.to_csv(corrected_path, index=False)
        self.logger.info(f"Corrected data saved to: {corrected_path}")

        try:
            mislabeled_path = self.config_manager.get_file_path('modeling', 'mislabeled_points')
        except ValueError:
            processed_data_dir = self.config_manager.get_config('paths').get('processed_data_dir', 'data/processed')
            mislabeled_path = str(Path(processed_data_dir) / 'potential_mislabeled.csv')

        Path(mislabeled_path).parent.mkdir(parents=True, exist_ok=True)
        mislabeled_df.to_csv(mislabeled_path, index=False)
        self.logger.info(f"Mislabeled points saved to: {mislabeled_path}")

        self.logger.info("Modeling pipeline completed successfully")

        return corrected_df, all_results


# Utility functions
def run_modeling_stage(config_manager: ConfigManager, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Run the modeling stage of the pipeline.

    Args:
        config_manager: Configuration manager instance
        df: Input DataFrame with features

    Returns:
        Tuple of (corrected DataFrame, modeling results)
    """
    trainer = ModelTrainer(config_manager)
    return trainer.run_modeling_pipeline(df)
