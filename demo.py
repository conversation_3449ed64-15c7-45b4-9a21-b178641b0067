"""
Healthcare ML Pipeline - Production Demo
Demonstrates the complete pipeline functionality with clean, production-ready code.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

def main():
    """Run a complete pipeline demonstration."""
    print("🏥 Healthcare ML Pipeline - Production Demo")
    print("=" * 60)
    print("This demo showcases the complete modularized ML pipeline.")
    print("The pipeline will automatically use AWS data if available,")
    print("or fall back to sample data for demonstration.\n")
    
    try:
        # Import the pipeline orchestrator
        from pipeline_orchestrator import PipelineOrchestrator
        
        print("📦 Initializing pipeline...")
        orchestrator = PipelineOrchestrator("config/pipeline_config.yaml")
        
        print("🚀 Running complete pipeline...")
        results = orchestrator.run_pipeline()
        
        if results.get('pipeline_success', False):
            print("\n🎉 Pipeline completed successfully!")
            print("\nResults Summary:")
            print(f"- Stages executed: {results.get('stages_executed', [])}")
            
            # Display data summary if available
            data_summary = results.get('data_summary', {})
            if data_summary:
                print(f"- Data processed: {data_summary}")
            
            print("\n📁 Generated Outputs:")
            print("- data/processed/ - Processed datasets")
            print("- outputs/visualizations/ - EDA plots and model comparisons")
            print("- models/ - Trained ML models")
            print("- logs/ - Detailed execution logs")
            
            print("\n📚 Next Steps:")
            print("1. Explore visualizations in outputs/visualizations/")
            print("2. Review processed data in data/processed/")
            print("3. Check model performance in models/")
            print("4. Modify config/pipeline_config.yaml and re-run")
            print("5. Extend the pipeline with custom features")
            
        else:
            print("\n❌ Pipeline execution failed.")
            if 'error' in results:
                print(f"Error: {results['error']}")
            print("\nCheck the logs/ directory for detailed error information.")
        
        return results.get('pipeline_success', False)
        
    except ImportError as e:
        print(f"❌ Failed to import pipeline modules: {e}")
        print("\nPlease ensure dependencies are installed:")
        print("pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        print("\nFor troubleshooting, see:")
        print("- QUICK_START.md for installation guidance")
        print("- logs/ directory for detailed error logs")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
