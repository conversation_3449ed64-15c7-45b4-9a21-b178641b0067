# Emerging Experts Pipeline

A comprehensive machine learning pipeline for identifying emerging healthcare experts using AWS Athena data, advanced feature engineering, and multiple ML algorithms.

## 📑 Table of Contents

- [🎯 Overview](#-overview)
- [🚀 Quick Start](#-quick-start)
- [🏗️ Pipeline Architecture](#️-pipeline-architecture)
- [🗄️ Data Sources](#️-data-sources)
- [📋 Step-by-Step Usage](#-step-by-step-usage)
- [✨ Key Features](#-key-features)
- [📁 Output Structure](#-output-structure)
- [🔧 Troubleshooting](#-troubleshooting)
- [📊 Model Interpretation](#-model-interpretation)
- [🤝 Support](#-support)

## 🎯 Overview

The Emerging Experts Pipeline analyzes healthcare professional data to identify emerging experts in medical fields. It processes publication records, clinical trial participation, conference presentations, and professional credentials to predict which healthcare professionals are likely to become recognized experts in their specialties.

**Key Capabilities:**
- Real-time AWS Athena data extraction using PyAthena
- Advanced healthcare-specific feature engineering
- Multiple ML algorithms with automated hyperparameter tuning
- Comprehensive model evaluation and comparison
- Production-ready modular architecture

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- AWS credentials configured
- Access to AWS Athena database: `cmg-oasis-prod-nba_medical_analyst-wkgrp`

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd emerging-experts-pipeline

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Configuration
1. **AWS Credentials**: Ensure AWS credentials are configured
   ```bash
   aws configure
   # OR set environment variables
   export AWS_ACCESS_KEY_ID=your_key
   export AWS_SECRET_ACCESS_KEY=your_secret
   export AWS_DEFAULT_REGION=us-west-2
   ```

2. **Pipeline Configuration**: Update `config/pipeline_config.yaml` with your AWS settings
   ```yaml
   etl:
     aws:
       database: "your_database_name"
       workgroup: "cmg-oasis-prod-nba_medical_analyst-wkgrp"
       region: "us-west-2"
       s3_bucket: "your_s3_bucket"
       s3_prefix: "your_prefix"
   ```

### Run Pipeline
```bash
# Run complete pipeline
python pipeline_orchestrator.py

# Test connectivity
python test_enhanced_etl.py
```

## 🏗️ Pipeline Architecture

The pipeline consists of 5 main stages executed sequentially:

### 1. **ETL (Extract, Transform, Load)**
- Extracts healthcare professional data from AWS Athena using PyAthena
- Transforms and validates data structure
- Handles publications, clinical trials, conferences, and professional credentials

### 2. **EDA (Exploratory Data Analysis)**
- Generates statistical summaries and data quality reports
- Creates visualizations for data distribution analysis
- Identifies patterns in healthcare professional activities

### 3. **Feature Engineering**
- Aggregates data at healthcare professional (HCP) level
- Creates advanced features from publication patterns, trial participation, and conference activity
- Implements label correction methodology for emerging expert identification

### 4. **Modeling**
- Trains multiple ML algorithms (Random Forest, Gradient Boosting, SVM, etc.)
- Performs automated hyperparameter tuning using Optuna
- Handles class imbalance and feature selection

### 5. **Evaluation**
- Comprehensive model performance evaluation
- Generates ROC curves, confusion matrices, and feature importance plots
- Compares models and selects best performer

## 📁 Project Structure

```
emerging-experts-pipeline/
├── config/
│   └── pipeline_config.yaml          # Main configuration file
├── src/
│   ├── etl/
│   │   └── data_extractor.py         # ETL module
│   ├── eda/
│   │   └── exploratory_analysis.py   # EDA module
│   ├── feature_engineering/
│   │   └── feature_processor.py      # Feature engineering module
│   ├── modeling/
│   │   └── model_trainer.py          # Modeling module
│   ├── evaluation/
│   │   └── model_evaluator.py        # Model evaluation module
│   └── utils/
│       ├── config_manager.py         # Configuration management
│       └── logger.py                 # Logging utilities
├── data/
│   ├── raw/                          # Raw data storage
│   ├── processed/                    # Processed data storage
│   └── outputs/                      # Output files
├── models/                           # Trained models storage
├── logs/                             # Log files
├── outputs/
│   └── visualizations/               # Generated plots and charts
├── pipeline_orchestrator.py          # Main pipeline controller
├── requirements.txt                  # Production dependencies
├── test_enhanced_etl.py              # ETL testing script
└── README.md                         # This file
```

## 🗄️ Data Sources

The Emerging Experts Pipeline extracts data from AWS Athena databases containing comprehensive healthcare professional information. Understanding these data sources is crucial for proper pipeline configuration and access management.

### **Primary Database**
- **Database Name**: `oasis_normalized`
- **AWS Region**: `us-west-2`
- **Workgroup**: `cmg-oasis-prod-nba_medical_analyst-wkgrp`

### **Core Data Tables**

#### **1. Healthcare Professional Identity & Matching**
- **`veeva_link_gene_link_mdm_match_qtrly`**
  - **Purpose**: Master data management matching table linking scientific experts to MDM IDs
  - **Key Fields**: `external_id_vod_c` (MDM ID), `se_id` (Scientific Expert ID)
  - **Update Frequency**: Quarterly
  - **Role**: Primary key linking table for all healthcare professional data

- **`veeva_link_scientific_expert_weekly`**
  - **Purpose**: Core healthcare professional profiles and emerging expert classifications
  - **Key Fields**: `se_id`, `source_npi`, `emerging_expert`, `job_title`, `source_first_name`, `source_last_name`, `source_specialty_1`
  - **Update Frequency**: Weekly
  - **Role**: Contains the target variable (`emerging_expert`) and basic HCP demographics

#### **2. Professional Credentials & Specialties**
- **`master_cm_mdm_profile`**
  - **Purpose**: Comprehensive healthcare professional profiles with NPI and specialty information
  - **Key Fields**: `mdm_id`, `npi_number`, `primary_phyn_spcl_desc`, `secondary_phyn_spcl_desc`
  - **Update Frequency**: Regular updates
  - **Role**: Links MDM IDs to NPI numbers and provides specialty classifications

#### **3. Publications Data**
- **`veeva_link_scientific_expert_publication_weekly`**
  - **Purpose**: Links healthcare professionals to their publications
  - **Key Fields**: `se_id`, `publication_id`, `authorship_position`
  - **Update Frequency**: Weekly
  - **Role**: Tracks publication authorship and positions (First Author, Last Author, Coauthor)

- **`veeva_link_publication_weekly`**
  - **Purpose**: Publication metadata and details
  - **Key Fields**: `publication_id`, `journal`, `publication_type`, `date_year_month`
  - **Update Frequency**: Weekly
  - **Role**: Provides publication details, journal information, and publication dates

#### **4. Clinical Trials Data**
- **`veeva_link_scientific_expert_clinical_trial_weekly`**
  - **Purpose**: Links healthcare professionals to clinical trials
  - **Key Fields**: `se_id`, `clinical_trial_id`, `role`
  - **Update Frequency**: Weekly
  - **Role**: Tracks HCP involvement in clinical trials and their roles

- **`veeva_link_clinical_trial_weekly`**
  - **Purpose**: Clinical trial metadata and details
  - **Key Fields**: `clinical_trial_id`, `name`, `phase`, `start_date`
  - **Update Frequency**: Weekly
  - **Role**: Provides clinical trial details, phases, and timeline information

#### **5. Conference & Events Data**
- **`veeva_link_event_attendee_weekly`**
  - **Purpose**: Links healthcare professionals to medical events and conferences
  - **Key Fields**: `se_id`, `medical_event_id`, `position`, `talk_title`, `talk_title_translation`
  - **Update Frequency**: Weekly
  - **Role**: Tracks speaking engagements and conference participation

- **`veeva_link_medical_event_weekly`**
  - **Purpose**: Medical event and conference metadata
  - **Key Fields**: `medical_event_id`, `name`, `city_name`, `country_name`, `start_date`
  - **Update Frequency**: Weekly
  - **Role**: Provides event details, locations, and dates

### **External Reference Data**
- **`sandbox_nba_medical_analyst.emerging_thought_leader_npidata_pfile`**
  - **Purpose**: NPI enumeration data for healthcare professionals
  - **Key Fields**: `npi`, `provider enumeration date`
  - **Role**: Provides NPI registration dates for career timeline analysis

### **Data Relationships & Joins**

#### **Primary Join Structure:**
```sql
Healthcare Professional Identity (MDM ID)
├── veeva_link_gene_link_mdm_match_qtrly (se_id ↔ external_id_vod_c)
├── veeva_link_scientific_expert_weekly (se_id → emerging_expert, demographics)
├── master_cm_mdm_profile (mdm_id → npi_number, specialties)
└── External NPI Data (npi → enumeration_year)

Publications Branch:
├── veeva_link_scientific_expert_publication_weekly (se_id ↔ publication_id)
└── veeva_link_publication_weekly (publication_id → journal, type, date)

Clinical Trials Branch:
├── veeva_link_scientific_expert_clinical_trial_weekly (se_id ↔ clinical_trial_id)
└── veeva_link_clinical_trial_weekly (clinical_trial_id → name, phase, date)

Events Branch:
├── veeva_link_event_attendee_weekly (se_id ↔ medical_event_id)
└── veeva_link_medical_event_weekly (medical_event_id → name, location, date)
```

#### **Key Relationships:**
1. **`se_id`** (Scientific Expert ID) - Primary linking key across all expert-related tables
2. **`external_id_vod_c`** (MDM ID) - Master data management identifier
3. **`npi_number`** - National Provider Identifier for healthcare professionals
4. **Foreign Key Relationships**: All activity tables (publications, trials, events) link back to healthcare professionals via `se_id`

### **Data Filtering & Selection Criteria**

#### **Target Population:**
- **Job Title Filter**: `LOWER(job_title) = 'fellow'` - Focuses on medical fellows
- **NPI Requirement**: `npi_number IS NOT NULL` - Ensures valid healthcare provider identification
- **Publication Filter**: Authorship positions in ('Last Author', 'First Author', 'Coauthor')
- **Publication Types**: 'Journal Article', 'Editorial', or Research Support publications
- **Event Participation**: Speaker positions at medical conferences

#### **Data Quality Requirements:**
- Valid MDM ID (`external_id_vod_c IS NOT NULL`)
- Active NPI numbers for professional verification
- Meaningful publication authorship positions
- Speaker-level conference participation (not just attendance)


#### **Configuration Validation:**
```bash
# Test PyAthena connectivity
python -c "
from pyathena import connect
from pyathena.pandas.cursor import PandasCursor

conn = connect(
    s3_staging_dir='s3://cmg-oasis-prod-athena-query-results-bucket/test/',
    region_name='us-west-2',
    work_group='cmg-oasis-prod-nba_medical_analyst-wkgrp',
    cursor_class=PandasCursor
)
print('✅ AWS Athena connection successful')
"
```

## 📋 Step-by-Step Usage

### Complete Pipeline Execution
```bash
# Run all stages sequentially
python pipeline_orchestrator.py

# Run with specific configuration
python pipeline_orchestrator.py --config custom_config.yaml

# Run with verbose logging
python pipeline_orchestrator.py --log-level DEBUG
```

### Individual Stage Execution
```bash
# Run specific stages
python pipeline_orchestrator.py --stages etl eda

# Run single stage
python pipeline_orchestrator.py --single-stage modeling

# Available stages: etl, eda, feature_engineering, modeling, evaluation
```

### Configuration Options
```bash
# List available options
python pipeline_orchestrator.py --help

# Run with custom parameters
python pipeline_orchestrator.py --stages etl modeling --output-dir custom_outputs
```

## ✨ Key Features

### **🔗 PyAthena Integration**
- Direct AWS Athena connectivity without Ray dependencies
- Avoids GLIBC compatibility issues
- Efficient data extraction with pandas integration

### **🎯 Real AWS Data Only**
- No sample data fallbacks - ensures production data integrity
- Fails gracefully when AWS connectivity unavailable
- Enforces proper AWS configuration and credentials

### **🧩 Modular Design**
- Independent, reusable pipeline stages
- YAML-based configuration management
- Comprehensive logging and error handling

### **📊 Advanced ML Capabilities**
- Multiple algorithms with hyperparameter optimization
- Automated feature selection and engineering
- Comprehensive model evaluation and comparison

### **🔍 Production Ready**
- Robust error handling and validation
- Detailed logging and monitoring
- Clean, maintainable code structure

## 📁 Output Structure

After running the pipeline, outputs are organized as follows:

```
outputs/
├── data/
│   ├── raw/                    # Raw extracted data
│   ├── processed/              # Transformed and validated data
│   └── features/               # Engineered features
├── visualizations/
│   ├── eda/                    # Exploratory data analysis plots
│   ├── feature_importance/     # Feature importance visualizations
│   └── model_evaluation/       # ROC curves, confusion matrices
├── models/
│   ├── trained_models/         # Serialized ML models
│   └── model_comparison/       # Model performance comparisons
└── reports/
    ├── data_quality/           # Data validation reports
    ├── model_performance/      # Detailed evaluation metrics
    └── pipeline_summary/       # Overall execution summary
```

### Key Output Files
- **`outputs/models/best_model.pkl`**: Best performing model
- **`outputs/reports/model_performance/evaluation_summary.json`**: Model metrics
- **`outputs/visualizations/model_evaluation/roc_curves.png`**: ROC curve comparison
- **`outputs/data/processed/emerging_experts_features.csv`**: Final feature dataset

## 🔧 Troubleshooting

### Common Issues

**PyAthena Import Error**
```bash
pip install PyAthena[pandas]
```

**AWS Credentials Not Found**
```bash
aws configure
# OR
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
```

**Database Access Denied**
- Verify workgroup: `cmg-oasis-prod-nba_medical_analyst-wkgrp`
- Check IAM permissions for Athena and S3 access
- Ensure region is set to `us-west-2`

**Pipeline Fails on Specific Stage**
```bash
# Check logs in logs/ directory
tail -f logs/pipeline_YYYYMMDD_HHMMSS.log

# Run individual stage for debugging
python pipeline_orchestrator.py --single-stage etl --log-level DEBUG
```

## 📊 Model Interpretation

### Understanding Results
- **Emerging Expert Score**: Probability (0-1) of becoming an emerging expert
- **Feature Importance**: Key factors driving predictions
- **Model Performance**: ROC-AUC, precision, recall metrics

### Business Value
- **Talent Identification**: Early identification of promising healthcare professionals
- **Resource Allocation**: Focus development resources on high-potential individuals
- **Network Building**: Connect emerging experts with established leaders
- **Research Collaboration**: Facilitate partnerships based on expertise predictions

## 🤝 Support

For issues, questions, or contributions:
1. Check the troubleshooting section above
2. Review logs in the `logs/` directory
3. Ensure AWS configuration is correct
4. Verify PyAthena installation and connectivity

---

**Pipeline Version**: 2.0
**Last Updated**: 2025
**Supported Python**: 3.8+
**AWS Region**: us-west-2
**Workgroup**: cmg-oasis-prod-nba_medical_analyst-wkgrp
