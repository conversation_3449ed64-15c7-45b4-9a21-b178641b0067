"""
Streamlit Feature Processor
Extracted and refactored feature engineering logic for Streamlit frontend.
Maintains compatibility with the original ML pipeline's feature engineering process.
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from typing import Dict, Any, List, Tuple, Optional
import logging
import json
from pathlib import Path


class StreamlitFeatureProcessor:
    """
    Handles feature engineering for single healthcare professional input in Streamlit app.
    Extracted from the original FeatureProcessor to work with individual HCP data.
    """

    def __init__(self, feature_stats_path: str = "data/processed/feature_statistics.json"):
        """
        Initialize the feature processor for Streamlit.

        Args:
            feature_stats_path: Path to feature statistics from training
        """
        self.feature_stats_path = feature_stats_path
        self.feature_stats = self._load_feature_stats()
        
        # Initialize scalers with training data statistics
        self.scaler = None
        self.imputer = None
        self._initialize_preprocessors()

    def _load_feature_stats(self) -> Dict[str, Any]:
        """Load feature statistics from training data."""
        try:
            with open(self.feature_stats_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default stats if file not found
            return self._get_default_feature_stats()

    def _get_default_feature_stats(self) -> Dict[str, Any]:
        """Get default feature statistics for fallback."""
        return {
            "numerical": {
                "publications_count": {"mean": 3.25, "std": 5.95},
                "first_author_count": {"mean": 3.62, "std": 23.59},
                "clinical_trials_count": {"mean": 0.11, "std": 0.47},
                "pi_roles_count": {"mean": 0.42, "std": 10.34},
                "conferences_count": {"mean": 0.99, "std": 1.93},
                "years_since_enumeration": {"mean": 5.04, "std": 2.57}
            }
        }

    def _initialize_preprocessors(self):
        """Initialize scalers and imputers with training statistics."""
        # For now, we'll create fresh preprocessors
        # In production, you'd want to save and load the fitted preprocessors
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='median')

    def calculate_hcp_features(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate features for a single HCP based on user input.
        
        Args:
            input_data: Dictionary containing user input data
            
        Returns:
            Dictionary of calculated features
        """
        features = {}
        
        # Basic information
        features['specialty'] = input_data.get('specialty', 'INTERNAL MEDICINE')
        
        # Publications features
        features['publications_count'] = input_data.get('publications_count', 0)
        features['first_author_count'] = input_data.get('first_author_count', 0)
        features['last_author_count'] = input_data.get('last_author_count', 0)
        features['coauthor_count'] = input_data.get('coauthor_count', 0)
        
        # Clinical trials features
        features['clinical_trials_count'] = input_data.get('clinical_trials_count', 0)
        features['pi_roles_count'] = input_data.get('pi_roles_count', 0)
        features['investigator_roles_count'] = input_data.get('investigator_roles_count', 0)
        
        # Conference features
        features['conferences_count'] = input_data.get('conferences_count', 0)
        features['conference_presentations_count'] = input_data.get('conference_presentations_count', 0)
        
        # Time-based features
        enumeration_year = input_data.get('enumeration_year')
        if enumeration_year:
            features['years_since_enumeration'] = 2024 - enumeration_year
            features['enumeration_year'] = enumeration_year
        else:
            features['years_since_enumeration'] = np.nan
            features['enumeration_year'] = np.nan
            
        # Publication years analysis
        first_pub_year = input_data.get('first_publication_year')
        latest_pub_year = input_data.get('latest_publication_year')
        
        if first_pub_year and latest_pub_year:
            features['first_publication_year'] = first_pub_year
            features['latest_publication_year'] = latest_pub_year
            features['publication_span_years'] = latest_pub_year - first_pub_year
            features['recent_publications_count'] = input_data.get('recent_publications_count', 0)
        else:
            features['first_publication_year'] = np.nan
            features['latest_publication_year'] = np.nan
            features['publication_span_years'] = np.nan
            features['recent_publications_count'] = 0
            
        # Conference years analysis
        first_conf_year = input_data.get('first_conference_year')
        latest_conf_year = input_data.get('latest_conference_year')
        
        if first_conf_year and latest_conf_year:
            features['first_conference_year'] = first_conf_year
            features['latest_conference_year'] = latest_conf_year
            features['conference_span_years'] = latest_conf_year - first_conf_year
            features['recent_conferences_count'] = input_data.get('recent_conferences_count', 0)
        else:
            features['first_conference_year'] = np.nan
            features['latest_conference_year'] = np.nan
            features['conference_span_years'] = np.nan
            features['recent_conferences_count'] = 0
            
        # Derived features
        features['total_activities'] = (features['publications_count'] + 
                                      features['clinical_trials_count'] + 
                                      features['conferences_count'])
        
        features['leadership_roles'] = features['pi_roles_count'] + features['first_author_count']
        
        # Activity ratios (avoid division by zero)
        if features['total_activities'] > 0:
            features['publications_ratio'] = features['publications_count'] / features['total_activities']
            features['trials_ratio'] = features['clinical_trials_count'] / features['total_activities']
            features['conferences_ratio'] = features['conferences_count'] / features['total_activities']
        else:
            features['publications_ratio'] = 0
            features['trials_ratio'] = 0
            features['conferences_ratio'] = 0
            
        return features

    def prepare_features_for_prediction(self, input_data: Dict[str, Any]) -> Tuple[np.ndarray, List[str]]:
        """
        Prepare features for model prediction.
        
        Args:
            input_data: User input data
            
        Returns:
            Tuple of (processed features array, feature names)
        """
        # Calculate all features
        features = self.calculate_hcp_features(input_data)
        
        # Get the numerical features used in modeling (from config)
        numerical_features = [
            "publications_count",
            "first_author_count", 
            "clinical_trials_count",
            "pi_roles_count",
            "conferences_count",
            "years_since_enumeration"
        ]
        
        # Create DataFrame with features
        feature_values = []
        for feature in numerical_features:
            value = features.get(feature, 0)
            # Handle NaN values
            if pd.isna(value):
                value = 0
            feature_values.append(value)
        
        # Convert to numpy array and reshape for single prediction
        X = np.array(feature_values).reshape(1, -1)
        
        # Apply same preprocessing as training
        X_imputed = self._apply_imputation(X)
        X_scaled = self._apply_scaling(X_imputed)
        
        return X_scaled, numerical_features

    def _apply_imputation(self, X: np.ndarray) -> np.ndarray:
        """Apply imputation using training statistics."""
        # For single prediction, we'll use the median values from training
        numerical_features = [
            "publications_count", "first_author_count", "clinical_trials_count",
            "pi_roles_count", "conferences_count", "years_since_enumeration"
        ]
        
        X_imputed = X.copy()
        for i, feature in enumerate(numerical_features):
            if np.isnan(X_imputed[0, i]):
                # Use median from training data
                median_val = self.feature_stats.get("numerical", {}).get(feature, {}).get("50%", 0)
                X_imputed[0, i] = median_val
                
        return X_imputed

    def _apply_scaling(self, X: np.ndarray) -> np.ndarray:
        """Apply scaling using training statistics."""
        numerical_features = [
            "publications_count", "first_author_count", "clinical_trials_count", 
            "pi_roles_count", "conferences_count", "years_since_enumeration"
        ]
        
        X_scaled = X.copy()
        for i, feature in enumerate(numerical_features):
            stats = self.feature_stats.get("numerical", {}).get(feature, {})
            mean_val = stats.get("mean", 0)
            std_val = stats.get("std", 1)
            
            if std_val > 0:
                X_scaled[0, i] = (X[0, i] - mean_val) / std_val
            else:
                X_scaled[0, i] = 0
                
        return X_scaled

    def get_feature_descriptions(self) -> Dict[str, str]:
        """Get human-readable descriptions for each feature."""
        return {
            "publications_count": "Total number of publications authored",
            "first_author_count": "Number of publications as first author",
            "clinical_trials_count": "Number of clinical trials participated in",
            "pi_roles_count": "Number of times served as Principal Investigator",
            "conferences_count": "Number of conferences presented at",
            "years_since_enumeration": "Years since medical license enumeration",
            "specialty": "Medical specialty/field of practice"
        }

    def get_feature_ranges(self) -> Dict[str, Dict[str, float]]:
        """Get reasonable input ranges for each feature based on training data."""
        return {
            "publications_count": {"min": 0, "max": 100, "step": 1},
            "first_author_count": {"min": 0, "max": 50, "step": 1},
            "clinical_trials_count": {"min": 0, "max": 15, "step": 1},
            "pi_roles_count": {"min": 0, "max": 20, "step": 1},
            "conferences_count": {"min": 0, "max": 50, "step": 1},
            "years_since_enumeration": {"min": 0, "max": 20, "step": 1},
            "enumeration_year": {"min": 2000, "max": 2024, "step": 1}
        }

    def get_specialty_options(self) -> List[str]:
        """Get list of medical specialties from training data."""
        specialty_stats = self.feature_stats.get("categorical", {}).get("specialty", {})
        if "value_counts" in specialty_stats:
            # Return top specialties, excluding 'nan'
            specialties = [k for k in specialty_stats["value_counts"].keys() if k != "nan"]
            return sorted(specialties)
        else:
            # Default specialties if stats not available
            return [
                "INTERNAL MEDICINE", "GENERAL PRACTICE", "PEDIATRICS", 
                "GENERAL SURGERY", "OBSTETRICS AND GYNECOLOGY", 
                "CARDIOVASCULAR DISEASE", "ORTHOPEDIC SURGERY", "NEUROLOGY"
            ]
