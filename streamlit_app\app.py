"""
Healthcare Emerging Expert Prediction - Streamlit Application
Main application file for the Streamlit frontend.
"""

import streamlit as st
import sys
from pathlib import Path
import traceback

# Add parent directory to path for imports
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

# Import pipeline components
from pipeline.feature_processor import StreamlitFeatureProcessor
from pipeline.model_predictor import ModelPredictor
from pipeline.data_validator import DataValidator

# Import UI components
from components.input_forms import InputForms
from components.prediction_display import PredictionDisplay
from components.model_explanation import ModelExplanation


def configure_page():
    """Configure Streamlit page settings."""
    st.set_page_config(
        page_title="Healthcare Emerging Expert Predictor",
        page_icon="🏥",
        layout="wide",
        initial_sidebar_state="expanded"
    )

def initialize_components():
    """Initialize all application components."""
    try:
        # Initialize pipeline components
        feature_processor = StreamlitFeatureProcessor()
        model_predictor = ModelPredictor()
        data_validator = DataValidator()
        
        # Initialize UI components
        input_forms = InputForms(feature_processor, data_validator)
        prediction_display = PredictionDisplay()
        model_explanation = ModelExplanation()
        
        return {
            'feature_processor': feature_processor,
            'model_predictor': model_predictor,
            'data_validator': data_validator,
            'input_forms': input_forms,
            'prediction_display': prediction_display,
            'model_explanation': model_explanation
        }
    except Exception as e:
        st.error(f"Failed to initialize application components: {str(e)}")
        st.error("Please ensure all required files are present and models are trained.")
        st.stop()

def render_header():
    """Render application header."""
    st.title("🏥 Healthcare Emerging Expert Predictor")
    st.markdown("""
    **Predict whether a healthcare professional is likely to become an emerging expert** based on their 
    publication record, clinical research involvement, conference presentations, and professional background.
    
    This tool uses machine learning trained on data from 5,675+ healthcare professionals to provide 
    evidence-based predictions with detailed explanations.
    """)
    st.divider()

def render_sidebar(components):
    """Render sidebar with model selection and information."""
    st.sidebar.header("⚙️ Configuration")
    
    # Model selection
    model_info = components['model_predictor'].get_model_info()
    available_models = model_info.get('available_models', [])
    best_model = model_info.get('best_model', 'gradient_boosting')
    
    selected_model = st.sidebar.selectbox(
        "Select Model",
        options=available_models,
        index=available_models.index(best_model) if best_model in available_models else 0,
        help="Choose which trained model to use for prediction"
    )
    
    # Model performance display
    if selected_model in model_info.get('model_performance', {}):
        perf = model_info['model_performance'][selected_model]
        st.sidebar.metric("Model Accuracy", f"{perf.get('accuracy', 0):.1%}")
        st.sidebar.metric("ROC-AUC Score", f"{perf.get('roc_auc', 0):.3f}")
    
    # Example profiles
    st.sidebar.header("📋 Example Profiles")
    example_profiles = components['input_forms'].get_example_profiles()
    
    selected_example = st.sidebar.selectbox(
        "Load Example Profile",
        options=["None"] + list(example_profiles.keys()),
        help="Load a pre-filled example to see how the model works"
    )
    
    return selected_model, selected_example

def main():
    """Main application function."""
    # Configure page
    configure_page()
    
    # Initialize components
    components = initialize_components()
    
    # Render header
    render_header()
    
    # Render sidebar and get selections
    selected_model, selected_example = render_sidebar(components)
    
    # Initialize session state for input data
    if 'input_data' not in st.session_state:
        st.session_state.input_data = {}
    
    # Load example profile if selected
    if selected_example != "None" and selected_example:
        example_data = components['input_forms'].load_example_profile(selected_example)
        st.session_state.input_data = example_data
        st.sidebar.success(f"Loaded: {selected_example}")
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        # Input form
        input_data = components['input_forms'].render_input_form()
        
        # Update session state
        st.session_state.input_data.update(input_data)
        
        # Validate input
        is_valid, errors = components['data_validator'].validate_input(input_data)
        components['input_forms'].render_validation_feedback(is_valid, errors)
        
        # Show suggestions
        if is_valid:
            suggestions = components['data_validator'].get_input_suggestions(input_data)
            components['input_forms'].render_suggestions(suggestions)
    
    with col2:
        # Prediction section
        if is_valid and st.button("🔮 Generate Prediction", type="primary", use_container_width=True):
            try:
                with st.spinner("Processing your data and generating prediction..."):
                    # Process features
                    X_processed, feature_names = components['feature_processor'].prepare_features_for_prediction(input_data)
                    
                    # Make prediction
                    prediction_result = components['model_predictor'].predict(X_processed, selected_model)
                    
                    # Add explanation
                    explanation = components['model_predictor'].get_prediction_explanation(
                        prediction_result, input_data
                    )
                    prediction_result['explanation'] = explanation
                    
                    # Store in session state
                    st.session_state.prediction_result = prediction_result
                    st.session_state.processed_input = input_data
                    
            except Exception as e:
                st.error(f"Prediction failed: {str(e)}")
                st.error("Please check your input data and try again.")
                
        # Display prediction results if available
        if 'prediction_result' in st.session_state:
            prediction_result = st.session_state.prediction_result
            processed_input = st.session_state.processed_input
            
            components['prediction_display'].render_prediction_results(prediction_result, processed_input)
    
    # Full-width sections below
    if 'prediction_result' in st.session_state:
        prediction_result = st.session_state.prediction_result
        processed_input = st.session_state.processed_input
        
        st.divider()
        
        # Detailed analysis tabs
        tab1, tab2, tab3, tab4 = st.tabs(["📊 Probability Analysis", "🎯 Feature Importance", "📈 Peer Comparison", "💡 Recommendations"])
        
        with tab1:
            components['prediction_display'].render_probability_visualization(prediction_result)
            
        with tab2:
            components['prediction_display'].render_feature_importance(prediction_result, processed_input)
            components['model_explanation'].render_feature_impact_analysis(prediction_result, processed_input)
            
        with tab3:
            components['prediction_display'].render_comparison_with_peers(processed_input)
            
        with tab4:
            components['prediction_display'].render_recommendations(prediction_result, processed_input)
    
    # Model information and methodology
    st.divider()
    
    # Model overview
    model_info = components['model_predictor'].get_model_info()
    components['model_explanation'].render_model_overview(model_info)
    
    # Methodology explanation
    components['model_explanation'].render_methodology_explanation()
    
    # Feedback section
    components['model_explanation'].render_feedback_section()

if __name__ == "__main__":
    main()
