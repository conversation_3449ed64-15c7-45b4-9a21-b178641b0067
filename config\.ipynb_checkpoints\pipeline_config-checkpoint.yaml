# Healthcare ML Pipeline Configuration
# This file controls the behavior and parameters of the entire pipeline

# Pipeline execution settings
pipeline:
  name: "healthcare_emerging_experts_pipeline"
  version: "1.0.0"
  stages:
    - etl
    - eda
    - feature_engineering
    - modeling
    - evaluation

  # Control which stages to execute (set to false to skip)
  execute_stages:
    etl: true
    eda: true
    feature_engineering: true
    modeling: true
    evaluation: true

# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_dir: "logs"
  log_file: "pipeline_{timestamp}.log"
  console_output: true
  file_output: true

# Data paths and directories
paths:
  data_dir: "data"
  raw_data_dir: "data/raw"
  processed_data_dir: "data/processed"
  models_dir: "models"
  outputs_dir: "outputs"
  visualizations_dir: "outputs/visualizations"

# ETL Configuration
etl:
  # AWS Configuration
  aws:
    database: "oasis_normalized"
    workgroup: "cmg-oasis-prod-nba_medical_analyst-wkgrp"
    s3_bucket: "cmg-oasis-prod-athena-query-results-bucket"
    s3_prefix: "nba_medical_analyst-AthenaQueryResults"

  # Data extraction settings
  extraction:
    query_timeout: 300  # seconds
    max_retries: 3
    chunk_size: 10000

  # Output files
  output_files:
    raw_data: "fellow_data_raw.csv"
    processed_data: "fellow_data_processed.csv"

# EDA Configuration
eda:
  # Visualization settings
  visualization:
    style: "seaborn-v0_8-whitegrid"
    palette: "viridis"
    figure_size: [12, 8]
    font_size: 12
    dpi: 300

  # Analysis parameters
  analysis:
    missing_value_threshold: 0.1  # Report columns with >10% missing values
    correlation_threshold: 0.7    # Flag high correlations
    outlier_detection: true
    class_distribution_analysis: true

  # Output files
  output_files:
    eda_report: "eda_report.html"
    class_distribution_plot: "class_distribution.png"
    feature_distributions_plot: "feature_distributions.png"
    correlation_matrix_plot: "correlation_matrix.png"

# Feature Engineering Configuration
feature_engineering:
  # Feature aggregation settings
  aggregation:
    group_by_column: "mdm_id"

  # Feature definitions
  features:
    numerical:
      - "publications_count"
      - "first_author_count"
      - "clinical_trials_count"
      - "pi_roles_count"
      - "conferences_count"
      - "years_since_enumeration"

    categorical:
      - "specialty"

  # Missing value handling
  missing_values:
    numerical_strategy: "median"  # mean, median, mode, constant
    categorical_strategy: "mode"  # mode, constant
    constant_value: 0

  # Feature scaling
  scaling:
    method: "standard"  # standard, minmax, robust, none

  # Output files
  output_files:
    aggregated_features: "hcp_aggregated_features.csv"
    feature_stats: "feature_statistics.json"

# Modeling Configuration
modeling:
  # Label correction settings
  label_correction:
    model_type: "logistic_regression"
    class_weight: "balanced"
    max_iter: 1000
    random_state: 42
    threshold_method: "youden_j"  # youden_j, f1_optimal, precision_recall

  # Model training settings
  training:
    test_size: 0.2
    validation_size: 0.2
    random_state: 42
    stratify: true
    cross_validation_folds: 5

  # Model types to train
  models:
    logistic_regression:
      class_weight: "balanced"
      max_iter: 1000
      random_state: 42

    random_forest:
      n_estimators: 100
      random_state: 42
      class_weight: "balanced"
      max_depth: null
      min_samples_split: 2
      min_samples_leaf: 1

    gradient_boosting:
      n_estimators: 100
      learning_rate: 0.1
      max_depth: 3
      random_state: 42

  # Output files
  output_files:
    corrected_labels: "hcp_corrected_labels.csv"
    mislabeled_points: "potential_mislabeled.csv"
    label_changes_summary: "label_changes_summary.csv"
    model_coefficients: "model_coefficients.csv"
    modeling_results: "modeling_results.json"

# Model Evaluation Configuration
evaluation:
  # Metrics to calculate
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "roc_auc"
    - "confusion_matrix"

  # Evaluation settings
  settings:
    cross_validation: true
    cv_folds: 5
    scoring_metric: "f1"
    generate_roc_curves: true
    generate_confusion_matrices: true
    feature_importance_analysis: true

  # Comparison settings
  comparison:
    compare_original_vs_corrected: true
    statistical_significance_test: true
    confidence_level: 0.95

  # Output files
  output_files:
    evaluation_report: "model_evaluation_report.json"
    roc_curves_plot: "roc_curves_comparison.png"
    confusion_matrices_plot: "confusion_matrices.png"
    feature_importance_plot: "feature_importance.png"
    model_comparison_plot: "model_comparison.png"

# Visualization Configuration
visualization:
  # Global settings
  global:
    save_plots: true
    show_plots: false  # Set to false for automated runs
    plot_format: "png"
    transparent_background: false

  # Specific plot settings
  plots:
    class_distribution:
      figsize: [10, 6]
      colors: ["#2D708EFF", "#20A387FF"]

    feature_distributions:
      figsize: [15, 10]
      subplot_layout: [2, 3]

    correlation_matrix:
      figsize: [12, 10]
      cmap: "coolwarm"
      annot: true

    roc_curves:
      figsize: [10, 8]
      line_width: 2

    radar_chart:
      figsize: [10, 8]
      alpha: 0.1

# Data Quality Checks
data_quality:
  # Validation rules
  validation:
    min_rows: 100
    max_missing_percentage: 50
    required_columns:
      - "mdm_id"
      - "emerging_expert"

  # Data type checks
  data_types:
    mdm_id: "object"
    emerging_expert: "object"
    publications_count: "numeric"
    conferences_count: "numeric"

  # Value range checks
  value_ranges:
    publications_count:
      min: 0
      max: 1000
    conferences_count:
      min: 0
      max: 500

# Performance and Resource Settings
performance:
  # Memory management
  memory:
    chunk_processing: true
    chunk_size: 10000
    low_memory_mode: false

  # Parallel processing
  parallel:
    enable: true
    n_jobs: -1  # Use all available cores
    backend: "threading"  # threading, multiprocessing

  # Caching
  caching:
    enable: true
    cache_dir: "cache"
    cache_models: true
    cache_features: true
