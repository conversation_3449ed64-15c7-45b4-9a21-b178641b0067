"""
Streamlit Input Forms
UI components for capturing healthcare professional data.
"""

import streamlit as st
from typing import Dict, Any, List
import datetime


class InputForms:
    """
    Handles user input forms for the Streamlit application.
    """

    def __init__(self, feature_processor, data_validator):
        """
        Initialize input forms.
        
        Args:
            feature_processor: Feature processor instance
            data_validator: Data validator instance
        """
        self.feature_processor = feature_processor
        self.data_validator = data_validator
        self.help_text = data_validator.get_field_help_text()
        self.feature_ranges = feature_processor.get_feature_ranges()
        self.specialty_options = feature_processor.get_specialty_options()

    def render_input_form(self) -> Dict[str, Any]:
        """
        Render the main input form and return user input data.
        
        Returns:
            Dictionary containing user input
        """
        st.header("Healthcare Professional Information")
        st.write("Please provide information about the healthcare professional to predict emerging expert status.")
        
        # Create tabs for different categories of inputs
        tab1, tab2, tab3, tab4 = st.tabs(["📚 Publications", "🔬 Clinical Trials", "🎤 Conferences", "👨‍⚕️ Professional Info"])
        
        input_data = {}
        
        # Publications tab
        with tab1:
            st.subheader("Publication Activity")
            
            col1, col2 = st.columns(2)
            
            with col1:
                input_data['publications_count'] = st.number_input(
                    "Total Publications",
                    min_value=self.feature_ranges['publications_count']['min'],
                    max_value=self.feature_ranges['publications_count']['max'],
                    value=0,
                    step=self.feature_ranges['publications_count']['step'],
                    help=self.help_text['publications_count']
                )
                
                input_data['first_author_count'] = st.number_input(
                    "First Author Publications",
                    min_value=self.feature_ranges['first_author_count']['min'],
                    max_value=min(self.feature_ranges['first_author_count']['max'], 
                                input_data['publications_count']),
                    value=0,
                    step=self.feature_ranges['first_author_count']['step'],
                    help=self.help_text['first_author_count']
                )
            
            with col2:
                # Publication timeline
                if input_data['publications_count'] > 0:
                    st.write("**Publication Timeline** (Optional)")
                    
                    current_year = datetime.datetime.now().year
                    input_data['first_publication_year'] = st.selectbox(
                        "First Publication Year",
                        options=[None] + list(range(1990, current_year + 1)),
                        index=0,
                        help="Year of your first publication"
                    )
                    
                    if input_data['first_publication_year']:
                        input_data['latest_publication_year'] = st.selectbox(
                            "Latest Publication Year", 
                            options=list(range(input_data['first_publication_year'], current_year + 1)),
                            index=-1,
                            help="Year of your most recent publication"
                        )
                        
                        input_data['recent_publications_count'] = st.number_input(
                            "Publications Since 2020",
                            min_value=0,
                            max_value=input_data['publications_count'],
                            value=min(input_data['publications_count'], 5),
                            help="Number of publications published since 2020"
                        )

        # Clinical Trials tab
        with tab2:
            st.subheader("Clinical Research Activity")
            
            col1, col2 = st.columns(2)
            
            with col1:
                input_data['clinical_trials_count'] = st.number_input(
                    "Clinical Trials Participated",
                    min_value=self.feature_ranges['clinical_trials_count']['min'],
                    max_value=self.feature_ranges['clinical_trials_count']['max'],
                    value=0,
                    step=self.feature_ranges['clinical_trials_count']['step'],
                    help=self.help_text['clinical_trials_count']
                )
                
            with col2:
                input_data['pi_roles_count'] = st.number_input(
                    "Principal Investigator Roles",
                    min_value=self.feature_ranges['pi_roles_count']['min'],
                    max_value=min(self.feature_ranges['pi_roles_count']['max'],
                                input_data['clinical_trials_count']),
                    value=0,
                    step=self.feature_ranges['pi_roles_count']['step'],
                    help=self.help_text['pi_roles_count']
                )
                
                # Auto-calculate investigator roles
                input_data['investigator_roles_count'] = max(0, 
                    input_data['clinical_trials_count'] - input_data['pi_roles_count'])

        # Conferences tab
        with tab3:
            st.subheader("Conference & Presentation Activity")
            
            col1, col2 = st.columns(2)
            
            with col1:
                input_data['conferences_count'] = st.number_input(
                    "Conferences Presented At",
                    min_value=self.feature_ranges['conferences_count']['min'],
                    max_value=self.feature_ranges['conferences_count']['max'],
                    value=0,
                    step=self.feature_ranges['conferences_count']['step'],
                    help=self.help_text['conferences_count']
                )
                
                # Auto-calculate presentation count (assuming 1 presentation per conference for simplicity)
                input_data['conference_presentations_count'] = input_data['conferences_count']
                
            with col2:
                if input_data['conferences_count'] > 0:
                    st.write("**Conference Timeline** (Optional)")
                    
                    current_year = datetime.datetime.now().year
                    input_data['first_conference_year'] = st.selectbox(
                        "First Conference Year",
                        options=[None] + list(range(2000, current_year + 1)),
                        index=0,
                        help="Year of your first conference presentation"
                    )
                    
                    if input_data['first_conference_year']:
                        input_data['latest_conference_year'] = st.selectbox(
                            "Latest Conference Year",
                            options=list(range(input_data['first_conference_year'], current_year + 1)),
                            index=-1,
                            help="Year of your most recent conference presentation"
                        )
                        
                        input_data['recent_conferences_count'] = st.number_input(
                            "Conferences Since 2020",
                            min_value=0,
                            max_value=input_data['conferences_count'],
                            value=min(input_data['conferences_count'], 3),
                            help="Number of conferences presented at since 2020"
                        )

        # Professional Info tab
        with tab4:
            st.subheader("Professional Information")
            
            col1, col2 = st.columns(2)
            
            with col1:
                input_data['specialty'] = st.selectbox(
                    "Medical Specialty",
                    options=self.specialty_options,
                    index=0,
                    help=self.help_text['specialty']
                )
                
            with col2:
                st.write("**License Information** (Optional)")
                
                input_data['enumeration_year'] = st.selectbox(
                    "Medical License Year",
                    options=[None] + list(range(1990, datetime.datetime.now().year + 1)),
                    index=0,
                    help=self.help_text['enumeration_year']
                )
                
                if input_data['enumeration_year']:
                    calculated_years = 2024 - input_data['enumeration_year']
                    input_data['years_since_enumeration'] = st.number_input(
                        "Years Since License",
                        min_value=0,
                        max_value=25,
                        value=calculated_years,
                        help=self.help_text['years_since_enumeration']
                    )
                else:
                    input_data['years_since_enumeration'] = None

        return input_data

    def render_input_summary(self, input_data: Dict[str, Any]) -> None:
        """
        Render a summary of the input data.
        
        Args:
            input_data: User input data
        """
        st.subheader("Input Summary")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Publications", input_data.get('publications_count', 0))
            st.metric("First Author Pubs", input_data.get('first_author_count', 0))
            
        with col2:
            st.metric("Clinical Trials", input_data.get('clinical_trials_count', 0))
            st.metric("PI Roles", input_data.get('pi_roles_count', 0))
            
        with col3:
            st.metric("Conferences", input_data.get('conferences_count', 0))
            years_since = input_data.get('years_since_enumeration')
            st.metric("Years Licensed", years_since if years_since else "Not specified")

    def render_validation_feedback(self, is_valid: bool, errors: List[str]) -> None:
        """
        Render validation feedback to the user.
        
        Args:
            is_valid: Whether input is valid
            errors: List of validation errors
        """
        if not is_valid:
            st.error("Please fix the following issues:")
            for error in errors:
                st.write(f"• {error}")
        else:
            st.success("✅ All inputs are valid!")

    def render_suggestions(self, suggestions: List[str]) -> None:
        """
        Render input suggestions to help users.
        
        Args:
            suggestions: List of suggestions
        """
        if suggestions:
            st.info("💡 **Suggestions to improve prediction accuracy:**")
            for suggestion in suggestions:
                st.write(f"• {suggestion}")

    def get_example_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Get example healthcare professional profiles for demonstration."""
        return {
            "Emerging Expert Example": {
                "publications_count": 25,
                "first_author_count": 8,
                "clinical_trials_count": 5,
                "pi_roles_count": 2,
                "conferences_count": 12,
                "years_since_enumeration": 8,
                "enumeration_year": 2016,
                "specialty": "HEMATOLOGY/ONCOLOGY",
                "recent_publications_count": 15,
                "recent_conferences_count": 8
            },
            "Early Career Professional": {
                "publications_count": 3,
                "first_author_count": 1,
                "clinical_trials_count": 1,
                "pi_roles_count": 0,
                "conferences_count": 2,
                "years_since_enumeration": 3,
                "enumeration_year": 2021,
                "specialty": "INTERNAL MEDICINE",
                "recent_publications_count": 3,
                "recent_conferences_count": 2
            },
            "Established Professional": {
                "publications_count": 45,
                "first_author_count": 15,
                "clinical_trials_count": 8,
                "pi_roles_count": 4,
                "conferences_count": 20,
                "years_since_enumeration": 12,
                "enumeration_year": 2012,
                "specialty": "CARDIOVASCULAR DISEASE",
                "recent_publications_count": 12,
                "recent_conferences_count": 6
            }
        }

    def load_example_profile(self, profile_name: str) -> Dict[str, Any]:
        """
        Load an example profile.
        
        Args:
            profile_name: Name of the example profile
            
        Returns:
            Example profile data
        """
        examples = self.get_example_profiles()
        return examples.get(profile_name, {})
