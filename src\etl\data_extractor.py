"""
ETL (Extract, Transform, Load) module for the Healthcare ML Pipeline.
Handles data extraction from AWS Athena, transformation, and loading.
"""

import pandas as pd
import numpy as np
import boto3
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
import logging
import warnings

from ..utils.logger import get_logger, log_function_call
from ..utils.config_manager import ConfigManager

# Global variables for AWS data access
HAS_PYATHENA = False
PYATHENA_ERROR = None

# Attempt to import PyAthena with error handling
def _check_pyathena_availability():
    """
    Check if PyAthena is available for AWS Athena data extraction.
    PyAthena doesn't have Ray dependency, avoiding GLIBC compatibility issues.
    """
    global HAS_PYATHENA, PYATHENA_ERROR

    try:
        from pyathena.pandas.cursor import PandasCursor
        from pyathena import connect
        HAS_PYATHENA = True
        return True

    except ImportError as e:
        error_msg = str(e).lower()
        if 'pyathena' in error_msg:
            PYATHENA_ERROR = "PyAthena package not installed"
        else:
            PYATHENA_ERROR = f"Import error: {str(e)}"
        return False

    except Exception as e:
        PYATHENA_ERROR = f"Unexpected error: {str(e)}"
        return False

# Check availability at module import time
_check_pyathena_availability()


class DataExtractor:
    """
    Handles data extraction from AWS Athena and initial data processing.
    """

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the data extractor.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.etl_config = config_manager.get_stage_config('etl')
        self.aws_config = config_manager.get_aws_config()
        self.logger = get_logger(__name__)

        # Log PyAthena availability status
        self._log_pyathena_status()

    def _log_pyathena_status(self):
        """Log the current PyAthena availability status with appropriate messaging."""
        if HAS_PYATHENA:
            self.logger.info("✅ PyAthena is available - AWS Athena data extraction enabled")
            self.logger.info("💡 PyAthena provides reliable AWS data access without Ray dependency issues")
        else:
            self.logger.error(f"❌ PyAthena not available: {PYATHENA_ERROR}")
            self.logger.error("❌ Pipeline requires PyAthena for AWS Athena data extraction")
            self.logger.error("❌ Pipeline cannot proceed without real AWS data access")

            # Provide helpful guidance based on error type
            if PYATHENA_ERROR and "not installed" in PYATHENA_ERROR.lower():
                self.logger.error("💡 Install PyAthena with: pip install PyAthena[pandas]")
                self.logger.error("💡 PyAthena is required for AWS Athena connectivity")
            else:
                self.logger.error("💡 PyAthena installation requirements:")
                self.logger.error("  - pip install PyAthena[pandas]")
                self.logger.error("  - Ensure pandas is installed: pip install pandas")
                self.logger.error("  - Verify AWS credentials are properly configured")

    @log_function_call()
    def extract_data(self) -> pd.DataFrame:
        """
        Extract data from AWS Athena using PyAthena.
        Pipeline requires successful AWS Athena connection and will fail if unavailable.

        Returns:
            Raw data DataFrame from AWS Athena

        Raises:
            RuntimeError: If PyAthena is not available
            Exception: If AWS Athena query execution fails
        """
        self.logger.info("🚀 Starting AWS Athena data extraction")

        # Check PyAthena availability first
        if not HAS_PYATHENA:
            error_msg = f"PyAthena is not available: {PYATHENA_ERROR}"
            self.logger.error(f"❌ {error_msg}")
            self.logger.error("❌ Pipeline requires PyAthena for AWS Athena data extraction")
            self.logger.error("❌ Install PyAthena with: pip install PyAthena[pandas]")
            raise RuntimeError(f"Pipeline cannot proceed without PyAthena. {error_msg}")

        start_time = time.time()

        try:
            self.logger.info("🔗 Connecting to AWS Athena using PyAthena")
            df = self._extract_from_athena_pyathena()

            end_time = time.time()
            execution_time = end_time - start_time

            self.logger.info(f"✅ AWS Athena data extraction completed successfully")
            self.logger.info(f"⏱️  Execution time: {execution_time:.2f} seconds")
            self.logger.info(f"📊 Retrieved {len(df)} rows and {len(df.columns)} columns")
            self.logger.info(f"🎯 Data source: AWS Athena (Real Data)")

            return df

        except Exception as e:
            self.logger.error(f"❌ AWS Athena data extraction failed: {str(e)}")
            self.logger.error("❌ Pipeline cannot proceed without successful AWS Athena connection")
            self.logger.error("💡 Check AWS credentials, network connectivity, and SQL query syntax")
            raise Exception(f"AWS Athena data extraction failed: {str(e)}. Pipeline requires real AWS data.")

    def _extract_from_athena_pyathena(self) -> pd.DataFrame:
        """Extract data from AWS Athena using PyAthena with PandasCursor."""
        if not HAS_PYATHENA:
            raise RuntimeError("PyAthena is not available for Athena extraction")

        from pyathena.pandas.cursor import PandasCursor
        from pyathena import connect

        # Get the SQL query
        sql_query = self._get_sql_query()

        # Set up AWS connection parameters
        s3_staging_dir = self._get_s3_staging_dir()
        region_name = self.aws_config.get('region', 'us-west-2')
        work_group = self.aws_config.get('workgroup', 'cmg-oasis-prod-nba_medical_analyst-wkgrp')

        self.logger.info(f"🔧 Executing Athena query using PyAthena:")
        self.logger.info(f"   Database: {self.aws_config['database']}")
        self.logger.info(f"   Workgroup: {work_group}")
        self.logger.info(f"   Region: {region_name}")
        self.logger.info(f"   S3 staging directory: {s3_staging_dir}")

        try:
            # Create PyAthena connection with PandasCursor
            pandas_cursor = connect(
                s3_staging_dir=s3_staging_dir,
                region_name=region_name,
                work_group=work_group,
                cursor_class=PandasCursor
            ).cursor()

            self.logger.info(f"📊 Executing SQL query...")

            # Execute the query and get DataFrame directly
            df = pandas_cursor.execute(sql_query).as_pandas()

            self.logger.info(f"✅ PyAthena query executed successfully")
            self.logger.info(f"   Retrieved {len(df)} rows and {len(df.columns)} columns")

            # Close the cursor
            pandas_cursor.close()

            return df

        except Exception as e:
            self.logger.error(f"❌ PyAthena query execution failed: {str(e)}")
            # Re-raise to be handled by the calling method
            raise





    def _get_sql_query(self) -> str:
        """
        Get the SQL query for data extraction.

        Returns:
            SQL query string
        """
        # This is the complex SQL query from the original script
        query = """
        WITH flag_fellow AS (
            SELECT mmq.external_id_vod_c mdm_id, mmq.se_id, sew.source_npi, sew.emerging_expert, sew.job_title,
            sew.source_first_name, sew.source_last_name,sew.source_specialty_1
            FROM "oasis_normalized"."veeva_link_gene_link_mdm_match_qtrly" mmq
            LEFT JOIN "oasis_normalized"."veeva_link_scientific_expert_weekly" sew
             ON sew.se_id = mmq.se_id
            WHERE mmq.external_id_vod_c IS NOT NULL
            AND LOWER(sew.job_title) = 'fellow'),

        fetch_npi as (
        select ff.*, cm.npi_number, cm.primary_phyn_spcl_desc, cm.secondary_phyn_spcl_desc from flag_fellow ff
        left outer join
        (select distinct mdm_id, npi_number, primary_phyn_spcl_desc, secondary_phyn_spcl_desc from oasis_normalized.master_cm_mdm_profile) cm
        on ff.mdm_id = cm.mdm_id),

        en_dt as (
        select npi,  try_cast(substr("provider enumeration date", 7,4 ) as INT) as enumeration_year from sandbox_nba_medical_analyst.emerging_thought_leader_npidata_pfile) ,

        join_fellow_en_dt as
        (select fnp.mdm_id,npi_number, fnp.job_title, fnp.primary_phyn_spcl_desc, fnp.secondary_phyn_spcl_desc, fnp.emerging_expert,  en_dt.enumeration_year from fetch_npi fnp
        left outer join
        en_dt
        on fnp.npi_number = en_dt.npi
        ),

        -- Clinical trials
        ct_name AS (
          SELECT mmq.external_id_vod_c mdm_id, ct.name ct_name, ct.phase ct_phase, sect.role ct_role, start_date
          FROM "oasis_normalized"."veeva_link_scientific_expert_clinical_trial_weekly" sect
          LEFT JOIN "oasis_normalized"."veeva_link_gene_link_mdm_match_qtrly" mmq
            ON mmq.se_id = sect.se_id
          LEFT JOIN "oasis_normalized"."veeva_link_clinical_trial_weekly" ct
            ON ct.clinical_trial_id = sect.clinical_trial_id
          WHERE mmq.external_id_vod_c IS NOT NULL
          GROUP BY mmq.external_id_vod_c, ct.name, ct.phase, ct.start_date, sect.role),

        ct_year as (
        select  mdm_id, ct_name, ct_phase, ct_role, date_format(DATE(start_date), '%Y') ct_year from ct_name),

        -- Publications
        hcp_pub_name AS (
          SELECT mmq.external_id_vod_c mdm_id,  pw.journal, pw.publication_type, sep.authorship_position, sep.publication_id, try_cast(substr(pw.date_year_month, 1,4 ) as INT) as pub_year
          FROM "oasis_normalized"."veeva_link_scientific_expert_publication_weekly" sep
          LEFT JOIN "oasis_normalized"."veeva_link_gene_link_mdm_match_qtrly" mmq
            ON mmq.se_id = sep.se_id
          LEFT JOIN "oasis_normalized"."veeva_link_publication_weekly" pw
            ON pw.publication_id = sep.publication_id
          WHERE sep.authorship_position IN ('Last Author', 'First Author', 'Coauthor')
            AND (pw.publication_type IN ('Journal Article', 'Editorial') OR pw.publication_type LIKE '%Research Support%')
            AND mmq.external_id_vod_c IS NOT NULL
        ),

        -- Conferences
        hcp_spk_tlk AS (
          SELECT mmq.external_id_vod_c mdm_id, mew.name event_name,
          COALESCE(NULLIF(ea.talk_title_translation,''), ea.talk_title) talk_title,
          mew.city_name,
          mew.country_name,
          DATE(mew.start_date) start_date,
          date_format(DATE(mew.start_date), '%M') start_month,
          cast(DAY(DATE(mew.start_date)) AS VARCHAR ) start_day,
          date_format(DATE(mew.start_date), '%Y') start_year
          FROM "oasis_normalized"."veeva_link_event_attendee_weekly" ea
          LEFT JOIN "oasis_normalized"."veeva_link_gene_link_mdm_match_qtrly" mmq
            ON mmq.se_id = ea.se_id
          LEFT JOIN "oasis_normalized"."veeva_link_medical_event_weekly" mew
            ON mew.medical_event_id = ea.medical_event_id
          WHERE ea.position = 'Speaker' AND  mmq.external_id_vod_c IS NOT NULL
        )

        select jfd.mdm_id, jfd.npi_number, jfd.job_title, jfd.primary_phyn_spcl_desc, jfd.secondary_phyn_spcl_desc, jfd.emerging_expert,  jfd.enumeration_year, ct.ct_name, ct.ct_phase, ct.ct_role, cast (ct.ct_year as INT) as ct_year, hpn.journal, hpn.publication_type, hpn.authorship_position, hpn.publication_id,cast(hpn.pub_year as INT) as pub_year,hst.event_name, hst.talk_title, cast(hst.start_year as INT) as conf_year from join_fellow_en_dt jfd
        left outer join
        ct_year ct
        on jfd.mdm_id = ct.mdm_id
        left outer join
        hcp_pub_name hpn
        on jfd.mdm_id = hpn.mdm_id
        left outer join
        hcp_spk_tlk hst
        on jfd.mdm_id = hst.mdm_id
        where npi_number is not null
        """

        return query

    def _get_s3_staging_dir(self) -> str:
        """
        Generate S3 staging directory path.

        Returns:
            S3 staging directory path
        """
        bucket = self.aws_config['s3_bucket']
        prefix = self.aws_config['s3_prefix']
        timestamp = datetime.now().strftime("%H:%M:%S")

        return f"s3://{bucket}/{prefix}/pyathena_{timestamp}"



    @log_function_call()
    def transform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply initial transformations to the raw data.

        Args:
            df: Raw data DataFrame

        Returns:
            Transformed DataFrame
        """
        self.logger.info("Starting data transformation")

        # Log initial data info
        self.logger.info(f"Input data shape: {df.shape}")
        self.logger.info(f"Columns: {list(df.columns)}")

        # Basic data cleaning
        df_transformed = df.copy()

        # Handle data types
        df_transformed = self._fix_data_types(df_transformed)

        # Handle missing values (basic cleaning)
        df_transformed = self._handle_missing_values(df_transformed)

        # Remove duplicates if any
        initial_rows = len(df_transformed)
        df_transformed = df_transformed.drop_duplicates()
        final_rows = len(df_transformed)

        if initial_rows != final_rows:
            self.logger.info(f"Removed {initial_rows - final_rows} duplicate rows")

        self.logger.info(f"Transformation completed. Final shape: {df_transformed.shape}")

        return df_transformed

    def _fix_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Fix data types for specific columns.

        Args:
            df: Input DataFrame

        Returns:
            DataFrame with corrected data types
        """
        # Convert year columns to numeric
        year_columns = ['enumeration_year', 'ct_year', 'pub_year', 'conf_year']
        for col in year_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure string columns are properly formatted
        string_columns = ['mdm_id', 'emerging_expert', 'job_title', 'primary_phyn_spcl_desc']
        for col in string_columns:
            if col in df.columns:
                df[col] = df[col].astype(str)

        return df

    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Handle missing values in the raw data.

        Args:
            df: Input DataFrame

        Returns:
            DataFrame with handled missing values
        """
        # Log missing value statistics
        missing_stats = df.isnull().sum()
        missing_pct = (missing_stats / len(df)) * 100

        self.logger.info("Missing value statistics:")
        for col, count in missing_stats.items():
            if count > 0:
                self.logger.info(f"  {col}: {count} ({missing_pct[col]:.1f}%)")

        # For now, we'll keep missing values as they will be handled in feature engineering
        # This is just basic cleaning

        return df

    @log_function_call()
    def load_data(self, df: pd.DataFrame, filename: str) -> str:
        """
        Save the processed data to file.

        Args:
            df: DataFrame to save
            filename: Output filename

        Returns:
            Full path to saved file
        """
        output_path = self.config_manager.get_file_path('etl', filename.replace('.csv', ''))

        try:
            df.to_csv(output_path, index=False)
            self.logger.info(f"Data saved to: {output_path}")
            self.logger.info(f"Saved {len(df)} rows and {len(df.columns)} columns")
            return output_path

        except Exception as e:
            self.logger.error(f"Failed to save data: {str(e)}")
            raise

    @log_function_call()
    def validate_data(self, df: pd.DataFrame) -> bool:
        """
        Validate the extracted and transformed data.

        Args:
            df: DataFrame to validate

        Returns:
            True if validation passes, False otherwise
        """
        self.logger.info("Validating extracted data")

        validation_config = self.config_manager.validate_data_quality_config()
        validation_rules = validation_config.get('validation', {})

        # Check minimum rows
        min_rows = validation_rules.get('min_rows', 100)
        if len(df) < min_rows:
            self.logger.error(f"Data validation failed: Only {len(df)} rows, minimum required: {min_rows}")
            return False

        # Check required columns
        required_columns = validation_rules.get('required_columns', [])
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"Data validation failed: Missing required columns: {missing_columns}")
            return False

        # Check maximum missing percentage
        max_missing_pct = validation_rules.get('max_missing_percentage', 50)
        missing_pct = (df.isnull().sum() / len(df)) * 100
        high_missing_cols = missing_pct[missing_pct > max_missing_pct]

        if not high_missing_cols.empty:
            self.logger.warning(f"Columns with high missing values: {dict(high_missing_cols)}")

        self.logger.info("Data validation completed successfully")
        return True

    def run_etl_pipeline(self) -> Tuple[pd.DataFrame, str]:
        """
        Run the complete ETL pipeline.

        Returns:
            Tuple of (processed DataFrame, output file path)
        """
        self.logger.info("Starting ETL pipeline")

        # Extract
        raw_data = self.extract_data()

        # Transform
        processed_data = self.transform_data(raw_data)

        # Validate
        if not self.validate_data(processed_data):
            raise ValueError("Data validation failed")

        # Load
        output_path = self.load_data(processed_data, 'raw_data')

        self.logger.info("ETL pipeline completed successfully")

        return processed_data, output_path

    @staticmethod
    def get_pyathena_status() -> Dict[str, Any]:
        """
        Get the current PyAthena availability status.

        Returns:
            Dictionary containing status information
        """
        # Determine data source
        if HAS_PYATHENA:
            data_source = 'AWS Athena (PyAthena)'
        else:
            data_source = 'Unavailable - PyAthena Required'

        return {
            'pyathena_available': HAS_PYATHENA,
            'aws_data_available': HAS_PYATHENA,
            'error': PYATHENA_ERROR,
            'data_source': data_source,
            'recommendations': _get_pyathena_recommendations()
        }

    @staticmethod
    def get_awswrangler_status() -> Dict[str, Any]:
        """
        Legacy method name for backward compatibility.
        Returns PyAthena status instead.
        """
        return DataExtractor.get_pyathena_status()


def _get_pyathena_recommendations() -> List[str]:
    """Get recommendations based on the current PyAthena error."""
    if not PYATHENA_ERROR:
        return []

    recommendations = []
    error_lower = PYATHENA_ERROR.lower()

    if 'not installed' in error_lower or 'pyathena' in error_lower:
        recommendations.extend([
            "Install PyAthena with pandas support: pip install PyAthena[pandas]",
            "Ensure pandas is installed: pip install pandas",
            "PyAthena is required for AWS Athena connectivity",
            "Pipeline cannot proceed without PyAthena - no sample data fallback available"
        ])
    else:
        recommendations.extend([
            "Check PyAthena installation: pip install PyAthena[pandas]",
            "Verify AWS credentials are properly configured",
            "Ensure pandas dependency is available",
            "Pipeline requires successful AWS Athena connection to proceed"
        ])

    return recommendations

def _get_awswrangler_recommendations() -> List[str]:
    """Legacy function for backward compatibility."""
    return _get_pyathena_recommendations()


# Utility functions
def run_etl_stage(config_manager: ConfigManager) -> Tuple[pd.DataFrame, str]:
    """
    Run the ETL stage of the pipeline.

    Args:
        config_manager: Configuration manager instance

    Returns:
        Tuple of (processed DataFrame, output file path)
    """
    extractor = DataExtractor(config_manager)
    return extractor.run_etl_pipeline()


# Example usage and testing
if __name__ == "__main__":
    from ..utils.config_manager import ConfigManager

    # Test ETL functionality
    try:
        config_manager = ConfigManager("config/pipeline_config.yaml")

        if config_manager.is_stage_enabled('etl'):
            data, output_path = run_etl_stage(config_manager)
            print(f"ETL completed successfully. Data shape: {data.shape}")
            print(f"Output saved to: {output_path}")
        else:
            print("ETL stage is disabled in configuration")

    except Exception as e:
        print(f"ETL test failed: {e}")
