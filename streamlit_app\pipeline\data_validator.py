"""
Data Validator for Streamlit Application
Handles input validation and data quality checks for user inputs.
"""

from typing import Dict, Any, List, Tuple, Optional
import pandas as pd
import numpy as np


class DataValidator:
    """
    Validates user input data for the Streamlit application.
    """

    def __init__(self):
        """Initialize the data validator."""
        self.validation_rules = self._get_validation_rules()

    def _get_validation_rules(self) -> Dict[str, Dict[str, Any]]:
        """Define validation rules for each input field."""
        return {
            "publications_count": {
                "type": "integer",
                "min": 0,
                "max": 200,
                "required": True
            },
            "first_author_count": {
                "type": "integer", 
                "min": 0,
                "max": 100,
                "required": True
            },
            "clinical_trials_count": {
                "type": "integer",
                "min": 0,
                "max": 50,
                "required": True
            },
            "pi_roles_count": {
                "type": "integer",
                "min": 0,
                "max": 30,
                "required": True
            },
            "conferences_count": {
                "type": "integer",
                "min": 0,
                "max": 100,
                "required": True
            },
            "years_since_enumeration": {
                "type": "integer",
                "min": 0,
                "max": 25,
                "required": False
            },
            "enumeration_year": {
                "type": "integer",
                "min": 1990,
                "max": 2024,
                "required": False
            },
            "specialty": {
                "type": "string",
                "required": True
            }
        }

    def validate_input(self, input_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate user input data.
        
        Args:
            input_data: Dictionary containing user input
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        for field, rules in self.validation_rules.items():
            value = input_data.get(field)
            
            # Check required fields
            if rules.get("required", False) and (value is None or value == ""):
                errors.append(f"{field.replace('_', ' ').title()} is required")
                continue
                
            # Skip validation for optional empty fields
            if value is None or value == "":
                continue
                
            # Type validation
            if rules["type"] == "integer":
                if not isinstance(value, (int, float)) or (isinstance(value, float) and not value.is_integer()):
                    errors.append(f"{field.replace('_', ' ').title()} must be a whole number")
                    continue
                    
                value = int(value)
                
                # Range validation for integers
                if "min" in rules and value < rules["min"]:
                    errors.append(f"{field.replace('_', ' ').title()} must be at least {rules['min']}")
                if "max" in rules and value > rules["max"]:
                    errors.append(f"{field.replace('_', ' ').title()} must be at most {rules['max']}")
                    
            elif rules["type"] == "string":
                if not isinstance(value, str):
                    errors.append(f"{field.replace('_', ' ').title()} must be text")

        # Business logic validation
        errors.extend(self._validate_business_logic(input_data))
        
        return len(errors) == 0, errors

    def _validate_business_logic(self, input_data: Dict[str, Any]) -> List[str]:
        """
        Validate business logic constraints.
        
        Args:
            input_data: User input data
            
        Returns:
            List of validation errors
        """
        errors = []
        
        # First author count cannot exceed total publications
        pub_count = input_data.get('publications_count', 0)
        first_author_count = input_data.get('first_author_count', 0)
        
        if first_author_count > pub_count:
            errors.append("First author count cannot exceed total publications count")
            
        # PI roles cannot exceed clinical trials
        ct_count = input_data.get('clinical_trials_count', 0)
        pi_count = input_data.get('pi_roles_count', 0)
        
        if pi_count > ct_count:
            errors.append("Principal Investigator roles cannot exceed clinical trials count")
            
        # Years since enumeration consistency
        enum_year = input_data.get('enumeration_year')
        years_since = input_data.get('years_since_enumeration')
        
        if enum_year and years_since:
            calculated_years = 2024 - enum_year
            if abs(calculated_years - years_since) > 1:  # Allow 1 year tolerance
                errors.append(f"Years since enumeration ({years_since}) doesn't match enumeration year ({enum_year})")
                
        return errors

    def sanitize_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize and clean user input data.
        
        Args:
            input_data: Raw user input
            
        Returns:
            Cleaned input data
        """
        sanitized = {}
        
        for field, value in input_data.items():
            if field in self.validation_rules:
                rules = self.validation_rules[field]
                
                if rules["type"] == "integer" and value is not None:
                    try:
                        sanitized[field] = int(float(value))
                    except (ValueError, TypeError):
                        sanitized[field] = 0
                elif rules["type"] == "string" and value is not None:
                    sanitized[field] = str(value).strip()
                else:
                    sanitized[field] = value
            else:
                sanitized[field] = value
                
        return sanitized

    def get_input_suggestions(self, input_data: Dict[str, Any]) -> List[str]:
        """
        Provide suggestions to improve input data quality.
        
        Args:
            input_data: User input data
            
        Returns:
            List of suggestions
        """
        suggestions = []
        
        pub_count = input_data.get('publications_count', 0)
        first_author = input_data.get('first_author_count', 0)
        ct_count = input_data.get('clinical_trials_count', 0)
        conf_count = input_data.get('conferences_count', 0)
        
        # Publication suggestions
        if pub_count == 0:
            suggestions.append("Consider adding publication data if available - publications are a strong predictor")
        elif first_author == 0 and pub_count > 0:
            suggestions.append("First author publications carry more weight - consider if any publications were first-authored")
            
        # Clinical trials suggestions
        if ct_count == 0:
            suggestions.append("Clinical trial participation is valuable for emerging expert prediction")
            
        # Conference suggestions
        if conf_count == 0:
            suggestions.append("Conference presentations demonstrate thought leadership and expertise")
            
        # Years since enumeration
        years_since = input_data.get('years_since_enumeration')
        if years_since and years_since < 2:
            suggestions.append("Very recent graduates may need more time to establish expertise patterns")
        elif years_since and years_since > 15:
            suggestions.append("Professionals with longer careers may have different expertise development patterns")
            
        return suggestions

    def get_field_help_text(self) -> Dict[str, str]:
        """Get help text for each input field."""
        return {
            "publications_count": "Total number of peer-reviewed publications authored or co-authored",
            "first_author_count": "Number of publications where you were the first/primary author",
            "clinical_trials_count": "Number of clinical trials you have participated in",
            "pi_roles_count": "Number of clinical trials where you served as Principal Investigator",
            "conferences_count": "Number of professional conferences where you presented",
            "years_since_enumeration": "Years since your medical license was first issued",
            "enumeration_year": "Year when your medical license was first issued",
            "specialty": "Your primary medical specialty or field of practice"
        }
