"""
Model Evaluation module for the Healthcare ML Pipeline.
Provides comprehensive model evaluation, comparison, and visualization.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, classification_report, roc_curve, auc
)
from sklearn.model_selection import cross_val_score
import joblib
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import logging
import json
from scipy import stats

from ..utils.logger import get_logger, log_function_call
from ..utils.config_manager import ConfigManager


class ModelEvaluator:
    """
    Handles comprehensive model evaluation and comparison.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the model evaluator.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.eval_config = config_manager.get_stage_config('evaluation')
        self.viz_config = config_manager.get_visualization_config()
        self.logger = get_logger(__name__)
        
        # Set up plotting style
        self._setup_plotting_style()
    
    def _setup_plotting_style(self):
        """Set up matplotlib and seaborn plotting style."""
        viz_settings = self.eval_config.get('visualization', {}) or self.viz_config.get('global', {})
        
        # Set default figure parameters
        plt.rcParams['figure.figsize'] = [12, 8]
        plt.rcParams['font.size'] = 12
        plt.rcParams['savefig.dpi'] = 300
    
    @log_function_call()
    def calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Calculate comprehensive evaluation metrics.
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            y_proba: Predicted probabilities (optional)
        
        Returns:
            Dictionary containing evaluation metrics
        """
        metrics = {}
        
        # Basic classification metrics
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        metrics['precision'] = precision_score(y_true, y_pred, average='binary')
        metrics['recall'] = recall_score(y_true, y_pred, average='binary')
        metrics['f1_score'] = f1_score(y_true, y_pred, average='binary')
        
        # Precision and recall for each class
        metrics['precision_class_0'] = precision_score(y_true, y_pred, pos_label=0, average='binary')
        metrics['precision_class_1'] = precision_score(y_true, y_pred, pos_label=1, average='binary')
        metrics['recall_class_0'] = recall_score(y_true, y_pred, pos_label=0, average='binary')
        metrics['recall_class_1'] = recall_score(y_true, y_pred, pos_label=1, average='binary')
        
        # ROC AUC if probabilities are provided
        if y_proba is not None:
            metrics['roc_auc'] = roc_auc_score(y_true, y_proba)
        
        # Confusion matrix components
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        metrics['true_negatives'] = tn
        metrics['false_positives'] = fp
        metrics['false_negatives'] = fn
        metrics['true_positives'] = tp
        
        # Additional derived metrics
        metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0
        metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0
        metrics['positive_predictive_value'] = tp / (tp + fp) if (tp + fp) > 0 else 0
        metrics['negative_predictive_value'] = tn / (tn + fn) if (tn + fn) > 0 else 0
        
        return metrics
    
    @log_function_call()
    def evaluate_model_performance(self, model, X_test: np.ndarray, y_test: np.ndarray, model_name: str) -> Dict[str, Any]:
        """
        Evaluate a single model's performance.
        
        Args:
            model: Trained model
            X_test: Test features
            y_test: Test labels
            model_name: Name of the model
        
        Returns:
            Dictionary containing evaluation results
        """
        self.logger.info(f"Evaluating model: {model_name}")
        
        # Generate predictions
        y_pred = model.predict(X_test)
        y_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
        
        # Calculate metrics
        metrics = self.calculate_metrics(y_test, y_pred, y_proba)
        
        # Generate classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Perform cross-validation if enabled
        cv_results = {}
        if self.eval_config.get('settings', {}).get('cross_validation', True):
            cv_folds = self.eval_config.get('settings', {}).get('cv_folds', 5)
            scoring_metric = self.eval_config.get('settings', {}).get('scoring_metric', 'f1')
            
            cv_scores = cross_val_score(model, X_test, y_test, cv=cv_folds, scoring=scoring_metric)
            cv_results = {
                'cv_scores': cv_scores.tolist(),
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'cv_metric': scoring_metric
            }
        
        evaluation_results = {
            'model_name': model_name,
            'metrics': metrics,
            'classification_report': class_report,
            'cross_validation': cv_results
        }
        
        self.logger.info(f"Model {model_name} evaluation completed. F1 Score: {metrics['f1_score']:.4f}")
        
        return evaluation_results
    
    @log_function_call()
    def compare_models(self, model_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Compare multiple models and rank them.
        
        Args:
            model_results: Dictionary of model evaluation results
        
        Returns:
            Dictionary containing model comparison results
        """
        self.logger.info("Comparing model performances")
        
        # Extract key metrics for comparison
        comparison_metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'roc_auc']
        
        comparison_data = {}
        for metric in comparison_metrics:
            comparison_data[metric] = {}
            for model_name, results in model_results.items():
                if 'metrics' in results:
                    comparison_data[metric][model_name] = results['metrics'].get(metric, np.nan)
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(comparison_data)
        
        # Rank models by F1 score (primary metric)
        if 'f1_score' in comparison_df.columns:
            comparison_df['rank'] = comparison_df['f1_score'].rank(ascending=False)
            best_model = comparison_df['f1_score'].idxmax()
        else:
            best_model = list(model_results.keys())[0] if model_results else None
        
        comparison_results = {
            'comparison_table': comparison_df.to_dict(),
            'best_model': best_model,
            'model_rankings': comparison_df.sort_values('rank').index.tolist() if 'rank' in comparison_df.columns else [],
            'summary_statistics': {
                'mean_metrics': comparison_df.mean().to_dict(),
                'std_metrics': comparison_df.std().to_dict()
            }
        }
        
        self.logger.info(f"Model comparison completed. Best model: {best_model}")
        
        return comparison_results
    
    @log_function_call()
    def create_confusion_matrix_plot(self, y_true: np.ndarray, y_pred: np.ndarray, model_name: str) -> str:
        """
        Create confusion matrix visualization.
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            model_name: Name of the model
        
        Returns:
            Path to saved plot
        """
        self.logger.info(f"Creating confusion matrix for {model_name}")
        
        # Calculate confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        
        # Create plot
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Non-Expert', 'Emerging Expert'],
                   yticklabels=['Non-Expert', 'Emerging Expert'])
        
        plt.title(f'Confusion Matrix - {model_name}')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        
        # Save plot
        output_path = self.config_manager.get_visualization_path(f'confusion_matrix_{model_name.lower()}.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=300)
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"Confusion matrix saved to: {output_path}")
        return output_path
    
    @log_function_call()
    def create_roc_curves_plot(self, model_results: Dict[str, Dict[str, Any]], X_test: np.ndarray, y_test: np.ndarray) -> str:
        """
        Create ROC curves comparison plot.
        
        Args:
            model_results: Dictionary of model results
            X_test: Test features
            y_test: Test labels
        
        Returns:
            Path to saved plot
        """
        self.logger.info("Creating ROC curves comparison plot")
        
        plt.figure(figsize=(10, 8))
        
        # Plot ROC curve for each model
        for model_name, results in model_results.items():
            if 'model' in results:
                model = results['model']
                if hasattr(model, 'predict_proba'):
                    y_proba = model.predict_proba(X_test)[:, 1]
                    fpr, tpr, _ = roc_curve(y_test, y_proba)
                    roc_auc = auc(fpr, tpr)
                    
                    plt.plot(fpr, tpr, lw=2, label=f'{model_name} (AUC = {roc_auc:.2f})')
        
        # Plot diagonal line
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
        
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves Comparison')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        
        # Save plot
        output_path = self.config_manager.get_visualization_path('roc_curves_comparison.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=300)
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"ROC curves plot saved to: {output_path}")
        return output_path
    
    @log_function_call()
    def create_feature_importance_plot(self, model, feature_names: List[str], model_name: str) -> str:
        """
        Create feature importance visualization.
        
        Args:
            model: Trained model
            feature_names: List of feature names
            model_name: Name of the model
        
        Returns:
            Path to saved plot
        """
        self.logger.info(f"Creating feature importance plot for {model_name}")
        
        # Get feature importance or coefficients
        if hasattr(model, 'feature_importances_'):
            importance = model.feature_importances_
            title = f'Feature Importance - {model_name}'
            xlabel = 'Importance'
        elif hasattr(model, 'coef_'):
            importance = np.abs(model.coef_[0])
            title = f'Feature Coefficients (Absolute) - {model_name}'
            xlabel = 'Absolute Coefficient'
        else:
            self.logger.warning(f"Model {model_name} does not have feature importance or coefficients")
            return ""
        
        # Create DataFrame for plotting
        importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Importance': importance
        }).sort_values('Importance', ascending=True)
        
        # Create plot
        plt.figure(figsize=(12, 8))
        sns.barplot(x='Importance', y='Feature', data=importance_df, palette='viridis')
        plt.title(title)
        plt.xlabel(xlabel)
        plt.tight_layout()
        
        # Save plot
        output_path = self.config_manager.get_visualization_path(f'feature_importance_{model_name.lower()}.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=300)
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"Feature importance plot saved to: {output_path}")
        return output_path
    
    @log_function_call()
    def create_model_comparison_plot(self, comparison_results: Dict[str, Any]) -> str:
        """
        Create model comparison visualization.
        
        Args:
            comparison_results: Model comparison results
        
        Returns:
            Path to saved plot
        """
        self.logger.info("Creating model comparison plot")
        
        # Extract comparison data
        comparison_data = comparison_results.get('comparison_table', {})
        
        if not comparison_data:
            self.logger.warning("No comparison data available for plotting")
            return ""
        
        # Create DataFrame
        comparison_df = pd.DataFrame(comparison_data)
        
        # Select key metrics for visualization
        metrics_to_plot = ['accuracy', 'precision', 'recall', 'f1_score']
        available_metrics = [m for m in metrics_to_plot if m in comparison_df.columns]
        
        if not available_metrics:
            self.logger.warning("No suitable metrics found for comparison plot")
            return ""
        
        # Create subplot for each metric
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.ravel()
        
        for i, metric in enumerate(available_metrics[:4]):
            if i < len(axes):
                ax = axes[i]
                metric_data = comparison_df[metric].sort_values(ascending=False)
                
                bars = ax.bar(range(len(metric_data)), metric_data.values, 
                             color=plt.cm.viridis(np.linspace(0, 1, len(metric_data))))
                
                ax.set_title(f'{metric.replace("_", " ").title()}')
                ax.set_xticks(range(len(metric_data)))
                ax.set_xticklabels(metric_data.index, rotation=45, ha='right')
                ax.set_ylabel('Score')
                
                # Add value labels on bars
                for j, bar in enumerate(bars):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{height:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # Save plot
        output_path = self.config_manager.get_visualization_path('model_comparison.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=300)
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"Model comparison plot saved to: {output_path}")
        return output_path
    
    @log_function_call()
    def save_evaluation_results(self, results: Dict[str, Any]) -> str:
        """
        Save evaluation results to JSON file.
        
        Args:
            results: Evaluation results dictionary
        
        Returns:
            Path to saved file
        """
        output_path = self.config_manager.get_file_path('evaluation', 'evaluation_report').replace('.csv', '.json')
        
        try:
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            self.logger.info(f"Evaluation results saved to: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"Failed to save evaluation results: {str(e)}")
            raise
    
    def run_evaluation_pipeline(self, model_results: Dict[str, Any], X_test: np.ndarray, y_test: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """
        Run the complete evaluation pipeline.
        
        Args:
            model_results: Dictionary containing trained models and results
            X_test: Test features
            y_test: Test labels
            feature_names: List of feature names
        
        Returns:
            Dictionary containing comprehensive evaluation results
        """
        self.logger.info("Starting evaluation pipeline")
        
        evaluation_results = {}
        
        # Evaluate each model
        for model_name, model_data in model_results.items():
            if 'model' in model_data:
                model = model_data['model']
                eval_result = self.evaluate_model_performance(model, X_test, y_test, model_name)
                evaluation_results[model_name] = eval_result
                
                # Create confusion matrix
                y_pred = model.predict(X_test)
                self.create_confusion_matrix_plot(y_test, y_pred, model_name)
                
                # Create feature importance plot
                self.create_feature_importance_plot(model, feature_names, model_name)
        
        # Compare models
        comparison_results = self.compare_models(evaluation_results)
        
        # Create comparison visualizations
        self.create_roc_curves_plot(model_results, X_test, y_test)
        self.create_model_comparison_plot(comparison_results)
        
        # Combine all results
        final_results = {
            'individual_evaluations': evaluation_results,
            'model_comparison': comparison_results,
            'evaluation_summary': {
                'n_models_evaluated': len(evaluation_results),
                'best_model': comparison_results.get('best_model'),
                'evaluation_date': pd.Timestamp.now().isoformat()
            }
        }
        
        # Save results
        self.save_evaluation_results(final_results)
        
        self.logger.info("Evaluation pipeline completed successfully")
        
        return final_results


# Utility functions
def run_evaluation_stage(config_manager: ConfigManager, model_results: Dict[str, Any], 
                        X_test: np.ndarray, y_test: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
    """
    Run the evaluation stage of the pipeline.
    
    Args:
        config_manager: Configuration manager instance
        model_results: Dictionary containing trained models and results
        X_test: Test features
        y_test: Test labels
        feature_names: List of feature names
    
    Returns:
        Comprehensive evaluation results
    """
    evaluator = ModelEvaluator(config_manager)
    return evaluator.run_evaluation_pipeline(model_results, X_test, y_test, feature_names)
