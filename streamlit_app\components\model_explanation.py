"""
Model Explanation Components
Provides detailed explanations of model behavior and predictions.
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from typing import Dict, Any, List


class ModelExplanation:
    """
    Handles model explanation and interpretation displays.
    """

    def __init__(self):
        """Initialize the model explanation component."""
        pass

    def render_model_overview(self, model_info: Dict[str, Any]) -> None:
        """
        Render overview of the ML model and its performance.
        
        Args:
            model_info: Information about available models
        """
        st.header("🤖 Model Information")
        
        best_model = model_info.get('best_model', 'gradient_boosting')
        available_models = model_info.get('available_models', [])
        
        st.write(f"**Primary Model:** {best_model.replace('_', ' ').title()}")
        st.write(f"**Available Models:** {', '.join([m.replace('_', ' ').title() for m in available_models])}")
        
        # Model performance comparison
        if 'model_performance' in model_info:
            self._render_model_comparison(model_info['model_performance'])

    def _render_model_comparison(self, model_performance: Dict[str, Dict[str, float]]) -> None:
        """Render model performance comparison."""
        st.subheader("Model Performance Comparison")
        
        # Create comparison dataframe
        comparison_data = []
        for model_name, metrics in model_performance.items():
            comparison_data.append({
                'Model': model_name.replace('_', ' ').title(),
                'Accuracy': metrics.get('accuracy', 0),
                'ROC-AUC': metrics.get('roc_auc', 0),
                'F1-Score': metrics.get('f1_score', 0)
            })
        
        if comparison_data:
            df = pd.DataFrame(comparison_data)
            
            # Display metrics table
            st.dataframe(df.round(3), use_container_width=True)
            
            # Create performance visualization
            fig = go.Figure()
            
            metrics = ['Accuracy', 'ROC-AUC', 'F1-Score']
            for metric in metrics:
                fig.add_trace(go.Bar(
                    name=metric,
                    x=df['Model'],
                    y=df[metric],
                    text=df[metric].round(3),
                    textposition='auto'
                ))
            
            fig.update_layout(
                title="Model Performance Metrics",
                xaxis_title="Models",
                yaxis_title="Score",
                barmode='group',
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)

    def render_feature_impact_analysis(self, prediction_result: Dict[str, Any],
                                     input_data: Dict[str, Any]) -> None:
        """
        Render detailed feature impact analysis.
        
        Args:
            prediction_result: Results from model prediction
            input_data: Original user input data
        """
        st.subheader("🔍 Feature Impact Analysis")
        
        feature_importance = prediction_result.get('feature_importance', {})
        
        if not feature_importance:
            st.warning("Feature importance data not available for this model.")
            return
            
        # Create impact analysis
        impact_data = []
        for feature, importance in feature_importance.items():
            user_value = input_data.get(feature, 0)
            
            # Determine impact level
            if importance > 0.3:
                impact_level = "High"
                impact_color = "🔴"
            elif importance > 0.1:
                impact_level = "Medium"
                impact_color = "🟡"
            else:
                impact_level = "Low"
                impact_color = "🟢"
                
            impact_data.append({
                'Feature': feature.replace('_', ' ').title(),
                'Your Value': user_value,
                'Importance': importance,
                'Impact Level': f"{impact_color} {impact_level}"
            })
        
        # Sort by importance
        impact_df = pd.DataFrame(impact_data).sort_values('Importance', ascending=False)
        
        # Display impact table
        st.dataframe(
            impact_df[['Feature', 'Your Value', 'Impact Level', 'Importance']].round(3),
            use_container_width=True
        )

    def render_what_if_analysis(self, current_input: Dict[str, Any]) -> None:
        """
        Render what-if analysis for exploring different scenarios.
        
        Args:
            current_input: Current user input data
        """
        st.subheader("🔄 What-If Analysis")
        
        st.write("Explore how changes to your profile might affect the prediction:")
        
        # Create scenarios
        scenarios = {
            "Increase Publications by 5": {**current_input, 'publications_count': current_input.get('publications_count', 0) + 5},
            "Add 2 Clinical Trials": {**current_input, 'clinical_trials_count': current_input.get('clinical_trials_count', 0) + 2},
            "Add 3 Conference Presentations": {**current_input, 'conferences_count': current_input.get('conferences_count', 0) + 3},
            "Become PI on 1 Trial": {**current_input, 'pi_roles_count': current_input.get('pi_roles_count', 0) + 1}
        }
        
        # Display scenarios as expandable sections
        for scenario_name, scenario_data in scenarios.items():
            with st.expander(f"📊 {scenario_name}"):
                st.write("**Modified Values:**")
                
                changes = []
                for key, value in scenario_data.items():
                    if current_input.get(key, 0) != value:
                        changes.append(f"- {key.replace('_', ' ').title()}: {current_input.get(key, 0)} → {value}")
                
                for change in changes:
                    st.write(change)
                
                st.write("*Click 'Run What-If Analysis' to see how this affects your prediction.*")

    def render_methodology_explanation(self) -> None:
        """Render explanation of the methodology and approach."""
        st.header("📖 Methodology")
        
        with st.expander("🔬 How the Model Works"):
            st.markdown("""
            **The Emerging Expert Prediction Model** uses machine learning to analyze patterns in healthcare professional activities:
            
            **Key Factors Analyzed:**
            - **Publication Activity**: Number and quality of research publications
            - **Clinical Research**: Participation in clinical trials and leadership roles
            - **Professional Engagement**: Conference presentations and thought leadership
            - **Career Timeline**: Years of experience and career progression
            
            **Model Training:**
            - Trained on data from 5,675+ healthcare professionals
            - Uses Gradient Boosting algorithm for optimal performance
            - Achieves 95.4% accuracy with 0.958 ROC-AUC score
            - Includes label correction to improve training data quality
            
            **Prediction Process:**
            1. Your input is processed through the same feature engineering pipeline used in training
            2. Features are scaled and normalized using training data statistics
            3. The trained model generates a probability score (0-100%)
            4. Results are interpreted with confidence intervals and explanations
            """)
            
        with st.expander("📊 Understanding Your Results"):
            st.markdown("""
            **Probability Score:**
            - **0-25%**: Low likelihood of emerging expert status
            - **25-50%**: Below average likelihood
            - **50-75%**: Above average likelihood  
            - **75-100%**: High likelihood of emerging expert status
            
            **Confidence Level:**
            - Indicates how certain the model is about its prediction
            - Higher confidence means the model has seen similar profiles in training
            - Lower confidence suggests your profile is unique or edge case
            
            **Feature Importance:**
            - Shows which aspects of your profile most influenced the prediction
            - Higher importance features have more impact on the final result
            - Use this to understand what drives emerging expert recognition
            """)
            
        with st.expander("⚠️ Important Disclaimers"):
            st.markdown("""
            **Model Limitations:**
            - Predictions are based on historical patterns and may not capture all factors
            - Results should be used as guidance, not definitive assessment
            - Individual career paths and expertise development vary significantly
            
            **Data Considerations:**
            - Model trained on specific healthcare professional dataset
            - May have biases present in training data
            - Performance may vary across different medical specialties
            
            **Recommendations:**
            - Use predictions as one input among many for career planning
            - Focus on continuous professional development regardless of prediction
            - Consider specialty-specific factors not captured in the model
            """)

    def render_feedback_section(self) -> None:
        """Render section for user feedback on predictions."""
        st.header("💬 Feedback")
        
        st.write("Help us improve the model by providing feedback on your prediction:")
        
        col1, col2 = st.columns(2)
        
        with col1:
            feedback_rating = st.selectbox(
                "How accurate do you think this prediction is?",
                ["Select...", "Very Accurate", "Somewhat Accurate", "Not Very Accurate", "Completely Inaccurate"]
            )
            
        with col2:
            feedback_comments = st.text_area(
                "Additional Comments (Optional)",
                placeholder="Any additional thoughts or context about your profile or the prediction..."
            )
        
        if st.button("Submit Feedback"):
            if feedback_rating != "Select...":
                st.success("Thank you for your feedback! This helps us improve the model.")
                # In a real application, you would save this feedback to a database
            else:
                st.warning("Please select a rating before submitting.")
