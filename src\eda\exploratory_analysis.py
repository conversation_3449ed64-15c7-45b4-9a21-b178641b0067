"""
Exploratory Data Analysis (EDA) module for the Healthcare ML Pipeline.
Provides comprehensive data exploration, visualization, and statistical analysis.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import logging
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

from ..utils.logger import get_logger, log_function_call
from ..utils.config_manager import ConfigManager


class ExploratoryAnalyzer:
    """
    Handles exploratory data analysis and visualization generation.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the exploratory analyzer.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.eda_config = config_manager.get_stage_config('eda')
        self.viz_config = config_manager.get_visualization_config()
        self.logger = get_logger(__name__)
        
        # Set up plotting style
        self._setup_plotting_style()
    
    def _setup_plotting_style(self):
        """Set up matplotlib and seaborn plotting style."""
        viz_settings = self.eda_config.get('visualization', {})
        
        # Set style
        style = viz_settings.get('style', 'seaborn-v0_8-whitegrid')
        plt.style.use(style)
        
        # Set palette
        palette = viz_settings.get('palette', 'viridis')
        sns.set_palette(palette)
        
        # Set default figure size and font size
        figsize = viz_settings.get('figure_size', [12, 8])
        font_size = viz_settings.get('font_size', 12)
        dpi = viz_settings.get('dpi', 300)
        
        plt.rcParams['figure.figsize'] = figsize
        plt.rcParams['font.size'] = font_size
        plt.rcParams['savefig.dpi'] = dpi
    
    @log_function_call()
    def analyze_dataset_overview(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate comprehensive dataset overview.
        
        Args:
            df: Input DataFrame
        
        Returns:
            Dictionary containing overview statistics
        """
        self.logger.info("Generating dataset overview")
        
        overview = {
            'shape': df.shape,
            'n_rows': df.shape[0],
            'n_columns': df.shape[1],
            'columns': list(df.columns),
            'dtypes': dict(df.dtypes),
            'memory_usage': df.memory_usage(deep=True).sum(),
            'duplicates': df.duplicated().sum()
        }
        
        # Missing values analysis
        missing_values = df.isnull().sum()
        missing_percentage = (missing_values / len(df)) * 100
        
        overview['missing_values'] = {
            'counts': dict(missing_values),
            'percentages': dict(missing_percentage),
            'total_missing': missing_values.sum(),
            'columns_with_missing': list(missing_values[missing_values > 0].index)
        }
        
        # Data type summary
        dtype_counts = df.dtypes.value_counts()
        overview['dtype_summary'] = dict(dtype_counts)
        
        self.logger.info(f"Dataset overview completed: {overview['n_rows']} rows, {overview['n_columns']} columns")
        
        return overview
    
    @log_function_call()
    def analyze_target_variable(self, df: pd.DataFrame, target_col: str = 'emerging_expert') -> Dict[str, Any]:
        """
        Analyze the target variable distribution.
        
        Args:
            df: Input DataFrame
            target_col: Target column name
        
        Returns:
            Dictionary containing target variable analysis
        """
        self.logger.info(f"Analyzing target variable: {target_col}")
        
        if target_col not in df.columns:
            self.logger.warning(f"Target column '{target_col}' not found in DataFrame")
            return {}
        
        # Class distribution
        class_counts = df[target_col].value_counts()
        class_percentages = df[target_col].value_counts(normalize=True) * 100
        
        target_analysis = {
            'class_counts': dict(class_counts),
            'class_percentages': dict(class_percentages),
            'n_classes': len(class_counts),
            'is_balanced': self._check_class_balance(class_percentages),
            'majority_class': class_counts.index[0],
            'minority_class': class_counts.index[-1],
            'imbalance_ratio': class_counts.iloc[0] / class_counts.iloc[-1] if len(class_counts) > 1 else 1.0
        }
        
        self.logger.info(f"Target analysis completed: {target_analysis['n_classes']} classes, "
                        f"imbalance ratio: {target_analysis['imbalance_ratio']:.2f}")
        
        return target_analysis
    
    def _check_class_balance(self, class_percentages: pd.Series, threshold: float = 40.0) -> bool:
        """
        Check if classes are reasonably balanced.
        
        Args:
            class_percentages: Series with class percentages
            threshold: Minimum percentage for minority class
        
        Returns:
            True if balanced, False otherwise
        """
        return class_percentages.min() >= threshold
    
    @log_function_call()
    def analyze_categorical_variables(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze categorical variables in the dataset.
        
        Args:
            df: Input DataFrame
        
        Returns:
            Dictionary containing categorical analysis
        """
        self.logger.info("Analyzing categorical variables")
        
        # Identify categorical columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        
        categorical_analysis = {}
        
        for col in categorical_cols:
            unique_values = df[col].nunique()
            value_counts = df[col].value_counts()
            
            categorical_analysis[col] = {
                'unique_count': unique_values,
                'top_values': dict(value_counts.head(10)),
                'missing_count': df[col].isnull().sum(),
                'missing_percentage': (df[col].isnull().sum() / len(df)) * 100
            }
            
            # Flag high cardinality columns
            if unique_values > len(df) * 0.5:
                categorical_analysis[col]['high_cardinality'] = True
        
        self.logger.info(f"Categorical analysis completed for {len(categorical_cols)} columns")
        
        return categorical_analysis
    
    @log_function_call()
    def analyze_numerical_variables(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze numerical variables in the dataset.
        
        Args:
            df: Input DataFrame
        
        Returns:
            Dictionary containing numerical analysis
        """
        self.logger.info("Analyzing numerical variables")
        
        # Identify numerical columns
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        numerical_analysis = {}
        
        for col in numerical_cols:
            desc_stats = df[col].describe()
            
            numerical_analysis[col] = {
                'count': desc_stats['count'],
                'mean': desc_stats['mean'],
                'std': desc_stats['std'],
                'min': desc_stats['min'],
                'q25': desc_stats['25%'],
                'median': desc_stats['50%'],
                'q75': desc_stats['75%'],
                'max': desc_stats['max'],
                'missing_count': df[col].isnull().sum(),
                'missing_percentage': (df[col].isnull().sum() / len(df)) * 100,
                'zeros_count': (df[col] == 0).sum(),
                'zeros_percentage': ((df[col] == 0).sum() / len(df)) * 100
            }
            
            # Detect potential outliers using IQR method
            Q1 = desc_stats['25%']
            Q3 = desc_stats['75%']
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
            numerical_analysis[col]['outliers_count'] = len(outliers)
            numerical_analysis[col]['outliers_percentage'] = (len(outliers) / len(df)) * 100
        
        self.logger.info(f"Numerical analysis completed for {len(numerical_cols)} columns")
        
        return numerical_analysis
    
    @log_function_call()
    def create_class_distribution_plot(self, df: pd.DataFrame, target_col: str = 'emerging_expert') -> str:
        """
        Create class distribution visualization.
        
        Args:
            df: Input DataFrame
            target_col: Target column name
        
        Returns:
            Path to saved plot
        """
        self.logger.info("Creating class distribution plot")
        
        if target_col not in df.columns:
            self.logger.warning(f"Target column '{target_col}' not found")
            return ""
        
        # Get plot configuration
        plot_config = self.viz_config.get('plots', {}).get('class_distribution', {})
        figsize = plot_config.get('figsize', [10, 6])
        colors = plot_config.get('colors', ['#2D708EFF', '#20A387FF'])
        
        # Create the plot
        plt.figure(figsize=figsize)
        
        class_counts = df[target_col].value_counts()
        class_percentages = df[target_col].value_counts(normalize=True) * 100
        
        ax = sns.barplot(x=class_counts.index, y=class_counts.values, palette=colors)
        
        # Add value labels on bars
        for i, (count, pct) in enumerate(zip(class_counts.values, class_percentages.values)):
            ax.annotate(f"{count}\n({pct:.1f}%)",
                       (i, count),
                       ha='center', va='bottom',
                       xytext=(0, 5), textcoords='offset points')
        
        plt.title('Distribution of Emerging Experts in the Dataset', fontsize=14)
        plt.xlabel('Emerging Expert', fontsize=12)
        plt.ylabel('Count', fontsize=12)
        
        # Save the plot
        output_path = self.config_manager.get_visualization_path('class_distribution.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=self.eda_config['visualization']['dpi'])
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"Class distribution plot saved to: {output_path}")
        return output_path
    
    @log_function_call()
    def create_missing_values_plot(self, df: pd.DataFrame) -> str:
        """
        Create missing values visualization.
        
        Args:
            df: Input DataFrame
        
        Returns:
            Path to saved plot
        """
        self.logger.info("Creating missing values plot")
        
        missing_data = df.isnull().sum()
        missing_data = missing_data[missing_data > 0].sort_values(ascending=False)
        
        if missing_data.empty:
            self.logger.info("No missing values found in dataset")
            return ""
        
        plt.figure(figsize=(12, 8))
        
        # Create bar plot
        ax = sns.barplot(x=missing_data.values, y=missing_data.index, palette='viridis')
        
        # Add percentage labels
        total_rows = len(df)
        for i, v in enumerate(missing_data.values):
            percentage = (v / total_rows) * 100
            ax.text(v + 0.1, i, f'{percentage:.1f}%', va='center')
        
        plt.title('Missing Values by Column', fontsize=14)
        plt.xlabel('Number of Missing Values', fontsize=12)
        plt.ylabel('Columns', fontsize=12)
        
        # Save the plot
        output_path = self.config_manager.get_visualization_path('missing_values.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=self.eda_config['visualization']['dpi'])
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"Missing values plot saved to: {output_path}")
        return output_path
    
    @log_function_call()
    def create_correlation_matrix(self, df: pd.DataFrame) -> str:
        """
        Create correlation matrix for numerical variables.
        
        Args:
            df: Input DataFrame
        
        Returns:
            Path to saved plot
        """
        self.logger.info("Creating correlation matrix")
        
        # Select only numerical columns
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if len(numerical_cols) < 2:
            self.logger.warning("Not enough numerical columns for correlation matrix")
            return ""
        
        # Calculate correlation matrix
        corr_matrix = df[numerical_cols].corr()
        
        # Get plot configuration
        plot_config = self.viz_config.get('plots', {}).get('correlation_matrix', {})
        figsize = plot_config.get('figsize', [12, 10])
        cmap = plot_config.get('cmap', 'coolwarm')
        annot = plot_config.get('annot', True)
        
        plt.figure(figsize=figsize)
        
        # Create heatmap
        sns.heatmap(corr_matrix, 
                   annot=annot, 
                   cmap=cmap, 
                   center=0,
                   square=True,
                   fmt='.2f')
        
        plt.title('Correlation Matrix of Numerical Variables', fontsize=14)
        plt.tight_layout()
        
        # Save the plot
        output_path = self.config_manager.get_visualization_path('correlation_matrix.png')
        plt.savefig(output_path, bbox_inches='tight', dpi=self.eda_config['visualization']['dpi'])
        
        if not self.viz_config.get('global', {}).get('show_plots', False):
            plt.close()
        
        self.logger.info(f"Correlation matrix saved to: {output_path}")
        return output_path
    
    def run_eda_pipeline(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Run the complete EDA pipeline.
        
        Args:
            df: Input DataFrame
        
        Returns:
            Dictionary containing all EDA results
        """
        self.logger.info("Starting EDA pipeline")
        
        eda_results = {}
        
        # Dataset overview
        eda_results['overview'] = self.analyze_dataset_overview(df)
        
        # Target variable analysis
        eda_results['target_analysis'] = self.analyze_target_variable(df)
        
        # Categorical variables analysis
        eda_results['categorical_analysis'] = self.analyze_categorical_variables(df)
        
        # Numerical variables analysis
        eda_results['numerical_analysis'] = self.analyze_numerical_variables(df)
        
        # Create visualizations
        eda_results['visualizations'] = {
            'class_distribution': self.create_class_distribution_plot(df),
            'missing_values': self.create_missing_values_plot(df),
            'correlation_matrix': self.create_correlation_matrix(df)
        }
        
        self.logger.info("EDA pipeline completed successfully")
        
        return eda_results


# Utility functions
def run_eda_stage(config_manager: ConfigManager, df: pd.DataFrame) -> Dict[str, Any]:
    """
    Run the EDA stage of the pipeline.
    
    Args:
        config_manager: Configuration manager instance
        df: Input DataFrame
    
    Returns:
        EDA results dictionary
    """
    analyzer = ExploratoryAnalyzer(config_manager)
    return analyzer.run_eda_pipeline(df)


# Example usage and testing
if __name__ == "__main__":
    from ..utils.config_manager import ConfigManager
    import pandas as pd
    
    # Test EDA functionality
    try:
        config_manager = ConfigManager("config/pipeline_config.yaml")
        
        # Create sample data for testing
        np.random.seed(42)
        sample_data = pd.DataFrame({
            'mdm_id': [f'HCP_{i}' for i in range(1000)],
            'emerging_expert': np.random.choice(['Y', 'N'], 1000, p=[0.2, 0.8]),
            'publications_count': np.random.poisson(3, 1000),
            'conferences_count': np.random.poisson(2, 1000),
            'specialty': np.random.choice(['Cardiology', 'Oncology', 'Neurology'], 1000)
        })
        
        if config_manager.is_stage_enabled('eda'):
            results = run_eda_stage(config_manager, sample_data)
            print("EDA completed successfully!")
            print(f"Dataset shape: {results['overview']['shape']}")
            print(f"Target classes: {results['target_analysis']['class_counts']}")
        else:
            print("EDA stage is disabled in configuration")
            
    except Exception as e:
        print(f"EDA test failed: {e}")
