"""
Healthcare ML Pipeline Orchestrator
Main entry point for the modularized healthcare emerging experts pipeline.
Supports dynamic execution of pipeline stages with comprehensive logging and configuration management.
"""

import sys
import argparse
import traceback
from pathlib import Path
from typing import Dict, Any, Optional
import pandas as pd

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / 'src'))

from src.utils.config_manager import ConfigManager
from src.utils.logger import PipelineLogger, StageLogger
from src.etl.data_extractor import run_etl_stage
from src.eda.exploratory_analysis import run_eda_stage
from src.feature_engineering.feature_processor import run_feature_engineering_stage
from src.modeling.model_trainer import run_modeling_stage
from src.evaluation.model_evaluator import run_evaluation_stage


class PipelineOrchestrator:
    """
    Main orchestrator for the healthcare ML pipeline.
    Manages stage execution, data flow, and error handling.
    """

    def __init__(self, config_path: str = "config/pipeline_config.yaml"):
        """
        Initialize the pipeline orchestrator.

        Args:
            config_path: Path to the configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.pipeline_logger = PipelineLogger(self.config_manager.get_config('logging'))
        self.logger = self.pipeline_logger.get_logger()

        # Pipeline state
        self.pipeline_data = {}
        self.stage_results = {}
        self.pipeline_success = True

        # Log pipeline initialization
        self.logger.info("Pipeline orchestrator initialized")
        self.pipeline_logger.log_config_info("Pipeline", self.config_manager.get_config('pipeline'))

    def run_pipeline(self, stages: Optional[list] = None) -> Dict[str, Any]:
        """
        Run the complete pipeline or specified stages.

        Args:
            stages: List of stages to run (optional, defaults to all enabled stages)

        Returns:
            Dictionary containing pipeline results
        """
        self.logger.info("Starting healthcare ML pipeline execution")

        # Determine which stages to run
        if stages is None:
            stages = self.config_manager.get_enabled_stages()

        self.logger.info(f"Pipeline stages to execute: {stages}")

        # Create run configuration
        run_config = self.config_manager.create_run_config()
        self.pipeline_logger.log_config_info("Run Configuration", run_config)

        try:
            # Execute each stage
            for stage in stages:
                if not self._execute_stage(stage):
                    self.pipeline_success = False
                    break

            # Generate final summary
            pipeline_summary = self._generate_pipeline_summary()

            if self.pipeline_success:
                self.logger.info("Pipeline execution completed successfully")
            else:
                self.logger.error("Pipeline execution failed")

            return pipeline_summary

        except Exception as e:
            self.logger.error(f"Pipeline execution failed with exception: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            self.pipeline_success = False
            return {"success": False, "error": str(e)}

    def _execute_stage(self, stage_name: str) -> bool:
        """
        Execute a single pipeline stage.

        Args:
            stage_name: Name of the stage to execute

        Returns:
            True if stage executed successfully, False otherwise
        """
        if not self.config_manager.is_stage_enabled(stage_name):
            self.logger.info(f"Stage '{stage_name}' is disabled, skipping")
            return True

        try:
            with StageLogger(self.pipeline_logger, stage_name):
                if stage_name == 'etl':
                    return self._run_etl_stage()
                elif stage_name == 'eda':
                    return self._run_eda_stage()
                elif stage_name == 'feature_engineering':
                    return self._run_feature_engineering_stage()
                elif stage_name == 'modeling':
                    return self._run_modeling_stage()
                elif stage_name == 'evaluation':
                    return self._run_evaluation_stage()
                else:
                    self.logger.error(f"Unknown stage: {stage_name}")
                    return False

        except Exception as e:
            self.logger.error(f"Stage '{stage_name}' failed: {str(e)}")
            return False

    def _run_etl_stage(self) -> bool:
        """Run the ETL stage."""
        try:
            self.logger.info("Executing ETL stage")

            # Run ETL
            raw_data, output_path = run_etl_stage(self.config_manager)

            # Store results
            self.pipeline_data['raw_data'] = raw_data
            self.stage_results['etl'] = {
                'output_path': output_path,
                'data_shape': raw_data.shape,
                'columns': list(raw_data.columns)
            }

            self.pipeline_logger.log_data_info("Raw Data", raw_data.shape, raw_data.isnull().sum().sum())
            self.pipeline_logger.log_file_operation("save", output_path)

            return True

        except Exception as e:
            self.logger.error(f"ETL stage failed: {str(e)}")
            return False

    def _run_eda_stage(self) -> bool:
        """Run the EDA stage."""
        try:
            self.logger.info("Executing EDA stage")

            # Check if we have data from ETL
            if 'raw_data' not in self.pipeline_data:
                self.logger.error("No raw data available for EDA. ETL stage must be run first.")
                return False

            # Run EDA
            eda_results = run_eda_stage(self.config_manager, self.pipeline_data['raw_data'])

            # Store results
            self.stage_results['eda'] = eda_results

            self.logger.info(f"EDA completed. Generated {len(eda_results.get('visualizations', {}))} visualizations")

            return True

        except Exception as e:
            self.logger.error(f"EDA stage failed: {str(e)}")
            return False

    def _run_feature_engineering_stage(self) -> bool:
        """Run the Feature Engineering stage."""
        try:
            self.logger.info("Executing Feature Engineering stage")

            # Check if we have data
            if 'raw_data' not in self.pipeline_data:
                self.logger.error("No raw data available for feature engineering. ETL stage must be run first.")
                return False

            # Run feature engineering
            processed_data, feature_stats = run_feature_engineering_stage(
                self.config_manager,
                self.pipeline_data['raw_data']
            )

            # Store results
            self.pipeline_data['processed_data'] = processed_data
            self.stage_results['feature_engineering'] = {
                'feature_stats': feature_stats,
                'processed_shape': processed_data.shape,
                'n_features': feature_stats['overview']['n_features']
            }

            self.pipeline_logger.log_data_info("Processed Data", processed_data.shape)
            self.logger.info(f"Feature engineering completed. Created {feature_stats['overview']['n_features']} features")

            return True

        except Exception as e:
            self.logger.error(f"Feature engineering stage failed: {str(e)}")
            return False

    def _run_modeling_stage(self) -> bool:
        """Run the Modeling stage."""
        try:
            self.logger.info("Executing Modeling stage")

            # Check if we have processed data
            if 'processed_data' not in self.pipeline_data:
                self.logger.error("No processed data available for modeling. Feature engineering stage must be run first.")
                return False

            # Run modeling
            corrected_data, modeling_results = run_modeling_stage(
                self.config_manager,
                self.pipeline_data['processed_data']
            )

            # Store results
            self.pipeline_data['corrected_data'] = corrected_data
            self.stage_results['modeling'] = modeling_results

            # Log modeling results
            correction_summary = modeling_results.get('correction_summary', {})
            self.logger.info(f"Modeling completed. Labels changed: {correction_summary.get('labels_changed', 0)}")
            self.logger.info(f"Models trained: {len(modeling_results.get('models', {}))}")

            return True

        except Exception as e:
            self.logger.error(f"Modeling stage failed: {str(e)}")
            return False

    def _run_evaluation_stage(self) -> bool:
        """Run the Evaluation stage."""
        try:
            self.logger.info("Executing Evaluation stage")

            # Check if we have modeling results
            if 'modeling' not in self.stage_results:
                self.logger.error("No modeling results available for evaluation. Modeling stage must be run first.")
                return False

            # Prepare data for evaluation (this is a simplified version)
            # In a real implementation, you'd need to properly split and prepare test data
            modeling_results = self.stage_results['modeling']

            # For now, we'll create a placeholder evaluation
            # This would need to be properly implemented with actual test data
            evaluation_results = {
                'evaluation_summary': {
                    'n_models_evaluated': len(modeling_results.get('models', {})),
                    'best_model': 'random_forest',  # Placeholder
                    'evaluation_date': pd.Timestamp.now().isoformat()
                },
                'note': 'Evaluation stage placeholder - requires proper test data implementation'
            }

            # Store results
            self.stage_results['evaluation'] = evaluation_results

            self.logger.info("Evaluation stage completed (placeholder implementation)")

            return True

        except Exception as e:
            self.logger.error(f"Evaluation stage failed: {str(e)}")
            return False

    def _generate_pipeline_summary(self) -> Dict[str, Any]:
        """
        Generate a comprehensive pipeline summary.

        Returns:
            Dictionary containing pipeline summary
        """
        summary = {
            'pipeline_success': self.pipeline_success,
            'stages_executed': list(self.stage_results.keys()),
            'stage_results': self.stage_results,
            'data_summary': {}
        }

        # Add data summaries
        if 'raw_data' in self.pipeline_data:
            summary['data_summary']['raw_data_shape'] = self.pipeline_data['raw_data'].shape

        if 'processed_data' in self.pipeline_data:
            summary['data_summary']['processed_data_shape'] = self.pipeline_data['processed_data'].shape

        if 'corrected_data' in self.pipeline_data:
            summary['data_summary']['corrected_data_shape'] = self.pipeline_data['corrected_data'].shape

        # Add configuration info
        summary['configuration'] = {
            'config_file': str(self.config_manager.config_path),
            'pipeline_version': self.config_manager.get_config('pipeline').get('version', '1.0.0')
        }

        return summary

    def run_single_stage(self, stage_name: str) -> bool:
        """
        Run a single pipeline stage.

        Args:
            stage_name: Name of the stage to run

        Returns:
            True if successful, False otherwise
        """
        self.logger.info(f"Running single stage: {stage_name}")
        return self._execute_stage(stage_name)

    def get_stage_results(self, stage_name: str) -> Optional[Dict[str, Any]]:
        """
        Get results from a specific stage.

        Args:
            stage_name: Name of the stage

        Returns:
            Stage results or None if not found
        """
        return self.stage_results.get(stage_name)

    def get_pipeline_data(self, data_key: str) -> Optional[Any]:
        """
        Get pipeline data by key.

        Args:
            data_key: Key for the data

        Returns:
            Pipeline data or None if not found
        """
        return self.pipeline_data.get(data_key)


def main():
    """Main entry point for the pipeline."""
    parser = argparse.ArgumentParser(description='Healthcare ML Pipeline')
    parser.add_argument('--config', default='config/pipeline_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--stages', nargs='+',
                       choices=['etl', 'eda', 'feature_engineering', 'modeling', 'evaluation'],
                       help='Specific stages to run (default: all enabled stages)')
    parser.add_argument('--single-stage',
                       choices=['etl', 'eda', 'feature_engineering', 'modeling', 'evaluation'],
                       help='Run only a single stage')

    args = parser.parse_args()

    try:
        # Initialize orchestrator
        orchestrator = PipelineOrchestrator(args.config)

        if args.single_stage:
            # Run single stage
            success = orchestrator.run_single_stage(args.single_stage)
            print(f"Stage '{args.single_stage}' {'completed successfully' if success else 'failed'}")
        else:
            # Run full pipeline or specified stages
            results = orchestrator.run_pipeline(args.stages)

            if results.get('pipeline_success', False):
                print("Pipeline completed successfully!")
                print(f"Stages executed: {results.get('stages_executed', [])}")
            else:
                print("Pipeline execution failed!")
                if 'error' in results:
                    print(f"Error: {results['error']}")

    except Exception as e:
        print(f"Pipeline execution failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
