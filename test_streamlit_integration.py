"""
Test script for Streamlit integration
Validates that the pipeline integration works correctly before launching the Streamlit app.
"""

import sys
from pathlib import Path
import traceback

# Add streamlit_app to path
sys.path.append(str(Path(__file__).parent / 'streamlit_app'))

def test_feature_processor():
    """Test the feature processor integration."""
    print("🧪 Testing Feature Processor...")
    
    try:
        from pipeline.feature_processor import StreamlitFeatureProcessor
        
        # Initialize processor
        processor = StreamlitFeatureProcessor()
        
        # Test with sample data
        sample_input = {
            'publications_count': 10,
            'first_author_count': 3,
            'clinical_trials_count': 2,
            'pi_roles_count': 1,
            'conferences_count': 5,
            'years_since_enumeration': 8,
            'enumeration_year': 2016,
            'specialty': 'INTERNAL MEDICINE'
        }
        
        # Test feature calculation
        features = processor.calculate_hcp_features(sample_input)
        print(f"   ✅ Feature calculation successful: {len(features)} features generated")
        
        # Test feature preparation for prediction
        X_processed, feature_names = processor.prepare_features_for_prediction(sample_input)
        print(f"   ✅ Feature preparation successful: {X_processed.shape}")
        
        # Test utility methods
        descriptions = processor.get_feature_descriptions()
        ranges = processor.get_feature_ranges()
        specialties = processor.get_specialty_options()
        
        print(f"   ✅ Utility methods working: {len(descriptions)} descriptions, {len(ranges)} ranges, {len(specialties)} specialties")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Feature processor test failed: {e}")
        traceback.print_exc()
        return False


def test_model_predictor():
    """Test the model predictor integration."""
    print("🤖 Testing Model Predictor...")
    
    try:
        from pipeline.model_predictor import ModelPredictor
        
        # Initialize predictor
        predictor = ModelPredictor()
        
        # Test model loading
        available_models = predictor.get_available_models()
        print(f"   ✅ Models loaded: {available_models}")
        
        # Test best model selection
        best_model = predictor.get_best_model_name()
        print(f"   ✅ Best model identified: {best_model}")
        
        # Test model info
        model_info = predictor.get_model_info()
        print(f"   ✅ Model info retrieved: {len(model_info.get('available_models', []))} models")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Model predictor test failed: {e}")
        traceback.print_exc()
        return False


def test_data_validator():
    """Test the data validator."""
    print("✅ Testing Data Validator...")
    
    try:
        from pipeline.data_validator import DataValidator
        
        # Initialize validator
        validator = DataValidator()
        
        # Test with valid data
        valid_input = {
            'publications_count': 10,
            'first_author_count': 3,
            'clinical_trials_count': 2,
            'pi_roles_count': 1,
            'conferences_count': 5,
            'specialty': 'INTERNAL MEDICINE'
        }
        
        is_valid, errors = validator.validate_input(valid_input)
        print(f"   ✅ Valid input test: {is_valid}, errors: {len(errors)}")
        
        # Test with invalid data
        invalid_input = {
            'publications_count': -1,  # Invalid negative
            'first_author_count': 15,  # More than total publications
            'clinical_trials_count': 2,
            'pi_roles_count': 5,  # More than total trials
            'conferences_count': 5,
            'specialty': 'INTERNAL MEDICINE'
        }
        
        is_valid, errors = validator.validate_input(invalid_input)
        print(f"   ✅ Invalid input test: {is_valid}, errors: {len(errors)}")
        
        # Test sanitization
        sanitized = validator.sanitize_input(invalid_input)
        print(f"   ✅ Input sanitization successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data validator test failed: {e}")
        traceback.print_exc()
        return False


def test_end_to_end_prediction():
    """Test end-to-end prediction pipeline."""
    print("🔮 Testing End-to-End Prediction...")
    
    try:
        from pipeline.feature_processor import StreamlitFeatureProcessor
        from pipeline.model_predictor import ModelPredictor
        from pipeline.data_validator import DataValidator
        
        # Initialize components
        processor = StreamlitFeatureProcessor()
        predictor = ModelPredictor()
        validator = DataValidator()
        
        # Test data
        test_input = {
            'publications_count': 15,
            'first_author_count': 5,
            'clinical_trials_count': 3,
            'pi_roles_count': 1,
            'conferences_count': 8,
            'years_since_enumeration': 6,
            'enumeration_year': 2018,
            'specialty': 'HEMATOLOGY/ONCOLOGY'
        }
        
        # Validate input
        is_valid, errors = validator.validate_input(test_input)
        if not is_valid:
            print(f"   ❌ Input validation failed: {errors}")
            return False
        
        # Process features
        X_processed, feature_names = processor.prepare_features_for_prediction(test_input)
        
        # Make prediction
        prediction_result = predictor.predict(X_processed)
        
        # Generate explanation
        explanation = predictor.get_prediction_explanation(prediction_result, test_input)
        
        print(f"   ✅ End-to-end prediction successful!")
        print(f"   📊 Prediction: {prediction_result['prediction_label']}")
        print(f"   📈 Probability: {prediction_result['probability_positive']:.1%}")
        print(f"   🎯 Confidence: {prediction_result['confidence']:.1%}")
        print(f"   🤖 Model: {prediction_result['model_used']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ End-to-end test failed: {e}")
        traceback.print_exc()
        return False


def check_file_structure():
    """Check if all required files are present."""
    print("📁 Checking File Structure...")
    
    required_files = [
        "streamlit_app/app.py",
        "streamlit_app/pipeline/feature_processor.py",
        "streamlit_app/pipeline/model_predictor.py",
        "streamlit_app/pipeline/data_validator.py",
        "streamlit_app/components/input_forms.py",
        "streamlit_app/components/prediction_display.py",
        "streamlit_app/components/model_explanation.py",
        "models/gradient_boosting_model.joblib",
        "data/processed/feature_statistics.json",
        "data/processed/modeling_results.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("   ❌ Missing required files:")
        for file in missing_files:
            print(f"      - {file}")
        return False
    else:
        print("   ✅ All required files present")
        return True


def main():
    """Run all integration tests."""
    print("🏥 Healthcare ML Pipeline - Streamlit Integration Test")
    print("=" * 60)
    
    tests = [
        ("File Structure", check_file_structure),
        ("Feature Processor", test_feature_processor),
        ("Model Predictor", test_model_predictor),
        ("Data Validator", test_data_validator),
        ("End-to-End Prediction", test_end_to_end_prediction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Ready to launch Streamlit app:")
        print("   python run_streamlit.py")
    else:
        print("\n⚠️ Some tests failed. Please fix issues before launching the app.")
        
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
