"""
Test core app functionality without launching Streamlit server
"""

import sys
from pathlib import Path

# Add streamlit_app to path
sys.path.append(str(Path(__file__).parent / 'streamlit_app'))

def test_app_components():
    """Test that all app components can be initialized."""
    print("🧪 Testing App Component Initialization...")
    
    try:
        # Test pipeline components
        from pipeline.feature_processor import StreamlitFeatureProcessor
        from pipeline.model_predictor import ModelPredictor
        from pipeline.data_validator import DataValidator
        
        # Test UI components
        from components.input_forms import InputForms
        from components.prediction_display import PredictionDisplay
        from components.model_explanation import ModelExplanation
        
        # Initialize components
        feature_processor = StreamlitFeatureProcessor()
        model_predictor = ModelPredictor()
        data_validator = DataValidator()
        
        input_forms = InputForms(feature_processor, data_validator)
        prediction_display = PredictionDisplay()
        model_explanation = ModelExplanation()
        
        print("   ✅ All components initialized successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Component initialization failed: {e}")
        return False

def test_example_prediction():
    """Test prediction with example data."""
    print("🔮 Testing Example Prediction...")
    
    try:
        from pipeline.feature_processor import StreamlitFeatureProcessor
        from pipeline.model_predictor import ModelPredictor
        from pipeline.data_validator import DataValidator
        
        # Initialize components
        processor = StreamlitFeatureProcessor()
        predictor = ModelPredictor()
        validator = DataValidator()
        
        # Test with emerging expert example
        example_data = {
            'publications_count': 25,
            'first_author_count': 8,
            'clinical_trials_count': 5,
            'pi_roles_count': 2,
            'conferences_count': 12,
            'years_since_enumeration': 8,
            'enumeration_year': 2016,
            'specialty': 'HEMATOLOGY/ONCOLOGY'
        }
        
        # Validate
        is_valid, errors = validator.validate_input(example_data)
        if not is_valid:
            print(f"   ❌ Validation failed: {errors}")
            return False
        
        # Process features
        X_processed, feature_names = processor.prepare_features_for_prediction(example_data)
        
        # Make prediction
        prediction_result = predictor.predict(X_processed)
        
        # Generate explanation
        explanation = predictor.get_prediction_explanation(prediction_result, example_data)
        
        print(f"   ✅ Prediction successful!")
        print(f"   📊 Result: {prediction_result['prediction_label']}")
        print(f"   📈 Probability: {prediction_result['probability_positive']:.1%}")
        print(f"   🤖 Model: {prediction_result['model_used']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Prediction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_input_validation():
    """Test input validation with various scenarios."""
    print("✅ Testing Input Validation...")
    
    try:
        from pipeline.data_validator import DataValidator
        
        validator = DataValidator()
        
        # Test valid input
        valid_input = {
            'publications_count': 10,
            'first_author_count': 3,
            'clinical_trials_count': 2,
            'pi_roles_count': 1,
            'conferences_count': 5,
            'specialty': 'INTERNAL MEDICINE'
        }
        
        is_valid, errors = validator.validate_input(valid_input)
        if not is_valid:
            print(f"   ❌ Valid input rejected: {errors}")
            return False
        
        # Test invalid input
        invalid_input = {
            'publications_count': -1,  # Invalid
            'first_author_count': 15,  # More than total
            'clinical_trials_count': 2,
            'pi_roles_count': 5,  # More than total
            'conferences_count': 5,
            'specialty': 'INTERNAL MEDICINE'
        }
        
        is_valid, errors = validator.validate_input(invalid_input)
        if is_valid:
            print("   ❌ Invalid input accepted")
            return False
        
        print(f"   ✅ Validation working correctly ({len(errors)} errors caught)")
        return True
        
    except Exception as e:
        print(f"   ❌ Validation test failed: {e}")
        return False

def test_feature_processing():
    """Test feature processing with different inputs."""
    print("🔧 Testing Feature Processing...")
    
    try:
        from pipeline.feature_processor import StreamlitFeatureProcessor
        
        processor = StreamlitFeatureProcessor()
        
        # Test with minimal input
        minimal_input = {
            'publications_count': 0,
            'first_author_count': 0,
            'clinical_trials_count': 0,
            'pi_roles_count': 0,
            'conferences_count': 0,
            'specialty': 'GENERAL PRACTICE'
        }
        
        features = processor.calculate_hcp_features(minimal_input)
        X_processed, feature_names = processor.prepare_features_for_prediction(minimal_input)
        
        print(f"   ✅ Minimal input processed: {len(features)} features, shape {X_processed.shape}")
        
        # Test with rich input
        rich_input = {
            'publications_count': 50,
            'first_author_count': 20,
            'clinical_trials_count': 10,
            'pi_roles_count': 5,
            'conferences_count': 25,
            'years_since_enumeration': 15,
            'enumeration_year': 2009,
            'specialty': 'CARDIOVASCULAR DISEASE'
        }
        
        features = processor.calculate_hcp_features(rich_input)
        X_processed, feature_names = processor.prepare_features_for_prediction(rich_input)
        
        print(f"   ✅ Rich input processed: {len(features)} features, shape {X_processed.shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Feature processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all functionality tests."""
    print("🏥 Healthcare ML Pipeline - App Functionality Test")
    print("=" * 60)
    
    tests = [
        ("Component Initialization", test_app_components),
        ("Input Validation", test_input_validation),
        ("Feature Processing", test_feature_processing),
        ("Example Prediction", test_example_prediction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Functionality Test Results:")
    print("-" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All functionality tests passed!")
        print("📱 The Streamlit app should work correctly when launched")
        print("🚀 To launch: streamlit run streamlit_app/app.py")
    else:
        print("\n⚠️ Some functionality tests failed.")
        
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
