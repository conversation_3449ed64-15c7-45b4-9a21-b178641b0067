"""
Configuration management utilities for the Healthcare ML Pipeline.
Handles loading, validation, and management of YAML configuration files.
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import json
from datetime import datetime


class ConfigManager:
    """
    Manages configuration loading, validation, and access for the pipeline.
    """
    
    def __init__(self, config_path: str = "config/pipeline_config.yaml"):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the main configuration file
        """
        self.config_path = Path(config_path)
        self.config = {}
        self._load_config()
        self._validate_config()
        self._create_directories()
    
    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as file:
                self.config = yaml.safe_load(file)
            print(f"Configuration loaded from: {self.config_path}")
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")
    
    def _validate_config(self):
        """Validate the loaded configuration."""
        required_sections = ['pipeline', 'logging', 'paths', 'etl', 'eda', 
                           'feature_engineering', 'modeling', 'evaluation']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate pipeline stages
        if 'stages' not in self.config['pipeline']:
            raise ValueError("Missing 'stages' in pipeline configuration")
        
        # Validate paths
        if 'data_dir' not in self.config['paths']:
            raise ValueError("Missing 'data_dir' in paths configuration")
    
    def _create_directories(self):
        """Create necessary directories based on configuration."""
        directories = [
            self.config['paths']['data_dir'],
            self.config['paths']['raw_data_dir'],
            self.config['paths']['processed_data_dir'],
            self.config['paths']['models_dir'],
            self.config['paths']['outputs_dir'],
            self.config['paths']['visualizations_dir'],
            self.config['logging']['log_dir']
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_config(self, section: Optional[str] = None) -> Dict[str, Any]:
        """
        Get configuration data.
        
        Args:
            section: Specific section to retrieve (optional)
        
        Returns:
            Configuration dictionary or section
        """
        if section:
            return self.config.get(section, {})
        return self.config
    
    def get_stage_config(self, stage_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific pipeline stage.
        
        Args:
            stage_name: Name of the pipeline stage
        
        Returns:
            Stage configuration dictionary
        """
        return self.config.get(stage_name, {})
    
    def is_stage_enabled(self, stage_name: str) -> bool:
        """
        Check if a pipeline stage is enabled for execution.
        
        Args:
            stage_name: Name of the pipeline stage
        
        Returns:
            True if stage is enabled, False otherwise
        """
        execute_stages = self.config.get('pipeline', {}).get('execute_stages', {})
        return execute_stages.get(stage_name, True)
    
    def get_enabled_stages(self) -> List[str]:
        """
        Get list of enabled pipeline stages.
        
        Returns:
            List of enabled stage names
        """
        all_stages = self.config.get('pipeline', {}).get('stages', [])
        execute_stages = self.config.get('pipeline', {}).get('execute_stages', {})
        
        return [stage for stage in all_stages if execute_stages.get(stage, True)]
    
    def get_file_path(self, category: str, file_key: str) -> str:
        """
        Get full file path for a specific file.
        
        Args:
            category: Configuration category (e.g., 'etl', 'modeling')
            file_key: File key within the category
        
        Returns:
            Full file path
        """
        base_dir = self.config['paths']['processed_data_dir']
        filename = self.config.get(category, {}).get('output_files', {}).get(file_key, '')
        
        if not filename:
            raise ValueError(f"File key '{file_key}' not found in category '{category}'")
        
        return str(Path(base_dir) / filename)
    
    def get_visualization_path(self, plot_name: str) -> str:
        """
        Get full path for visualization files.
        
        Args:
            plot_name: Name of the plot file
        
        Returns:
            Full path to visualization file
        """
        viz_dir = self.config['paths']['visualizations_dir']
        return str(Path(viz_dir) / plot_name)
    
    def get_model_path(self, model_name: str) -> str:
        """
        Get full path for model files.
        
        Args:
            model_name: Name of the model file
        
        Returns:
            Full path to model file
        """
        models_dir = self.config['paths']['models_dir']
        return str(Path(models_dir) / model_name)
    
    def update_config(self, section: str, key: str, value: Any):
        """
        Update a configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: New value
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
    
    def save_config(self, output_path: Optional[str] = None):
        """
        Save current configuration to file.
        
        Args:
            output_path: Output file path (optional)
        """
        save_path = output_path or self.config_path
        
        with open(save_path, 'w') as file:
            yaml.dump(self.config, file, default_flow_style=False, indent=2)
        
        print(f"Configuration saved to: {save_path}")
    
    def export_config_json(self, output_path: str):
        """
        Export configuration as JSON file.
        
        Args:
            output_path: Output JSON file path
        """
        with open(output_path, 'w') as file:
            json.dump(self.config, file, indent=2, default=str)
        
        print(f"Configuration exported to JSON: {output_path}")
    
    def get_aws_config(self) -> Dict[str, Any]:
        """Get AWS configuration for ETL operations."""
        return self.config.get('etl', {}).get('aws', {})
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific model.
        
        Args:
            model_name: Name of the model
        
        Returns:
            Model configuration dictionary
        """
        models_config = self.config.get('modeling', {}).get('models', {})
        return models_config.get(model_name, {})
    
    def get_features_config(self) -> Dict[str, Any]:
        """Get feature engineering configuration."""
        return self.config.get('feature_engineering', {}).get('features', {})
    
    def get_visualization_config(self, plot_type: str = None) -> Dict[str, Any]:
        """
        Get visualization configuration.
        
        Args:
            plot_type: Specific plot type (optional)
        
        Returns:
            Visualization configuration
        """
        viz_config = self.config.get('visualization', {})
        
        if plot_type:
            return viz_config.get('plots', {}).get(plot_type, {})
        
        return viz_config
    
    def validate_data_quality_config(self) -> Dict[str, Any]:
        """Get data quality validation configuration."""
        return self.config.get('data_quality', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance and resource configuration."""
        return self.config.get('performance', {})
    
    def create_run_config(self) -> Dict[str, Any]:
        """
        Create a run-specific configuration with timestamps and metadata.
        
        Returns:
            Run configuration dictionary
        """
        run_config = {
            'run_id': datetime.now().strftime('%Y%m%d_%H%M%S'),
            'timestamp': datetime.now().isoformat(),
            'config_version': self.config.get('pipeline', {}).get('version', '1.0.0'),
            'enabled_stages': self.get_enabled_stages(),
            'config_file': str(self.config_path)
        }
        
        return run_config
    
    def __str__(self) -> str:
        """String representation of the configuration."""
        return f"ConfigManager(config_path={self.config_path}, stages={len(self.config.get('pipeline', {}).get('stages', []))})"
    
    def __repr__(self) -> str:
        """Detailed representation of the configuration."""
        return self.__str__()


# Utility functions
def load_config(config_path: str = "config/pipeline_config.yaml") -> ConfigManager:
    """
    Load configuration from file.
    
    Args:
        config_path: Path to configuration file
    
    Returns:
        ConfigManager instance
    """
    return ConfigManager(config_path)


def validate_config_file(config_path: str) -> bool:
    """
    Validate a configuration file without loading it into a manager.
    
    Args:
        config_path: Path to configuration file
    
    Returns:
        True if valid, False otherwise
    """
    try:
        ConfigManager(config_path)
        return True
    except (FileNotFoundError, ValueError, yaml.YAMLError):
        return False


# Example usage and testing
if __name__ == "__main__":
    # Test configuration manager
    try:
        config_manager = ConfigManager("config/pipeline_config.yaml")
        
        print("Configuration loaded successfully!")
        print(f"Enabled stages: {config_manager.get_enabled_stages()}")
        print(f"ETL config: {config_manager.get_stage_config('etl')}")
        print(f"AWS config: {config_manager.get_aws_config()}")
        
        # Test file path generation
        etl_file = config_manager.get_file_path('etl', 'raw_data')
        print(f"ETL raw data file path: {etl_file}")
        
        # Test run configuration
        run_config = config_manager.create_run_config()
        print(f"Run configuration: {run_config}")
        
    except Exception as e:
        print(f"Error testing configuration manager: {e}")
