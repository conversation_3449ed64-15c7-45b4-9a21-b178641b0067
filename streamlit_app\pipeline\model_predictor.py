"""
Streamlit Model Predictor
Handles loading trained models and making predictions for the Streamlit frontend.
"""

import joblib
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import logging
import json


class ModelPredictor:
    """
    Handles model loading and prediction for Streamlit application.
    """

    def __init__(self, models_dir: str = "models", results_path: str = "data/processed/modeling_results.json"):
        """
        Initialize the model predictor.

        Args:
            models_dir: Directory containing trained models
            results_path: Path to modeling results with performance metrics
        """
        self.models_dir = Path(models_dir)
        self.results_path = results_path
        self.models = {}
        self.model_metadata = {}
        
        # Load modeling results for model selection and metadata
        self._load_modeling_results()
        
        # Load available models
        self._load_models()

    def _load_modeling_results(self):
        """Load modeling results to get performance metrics and model metadata."""
        try:
            with open(self.results_path, 'r') as f:
                self.model_metadata = json.load(f)
        except FileNotFoundError:
            logging.warning(f"Modeling results file not found: {self.results_path}")
            self.model_metadata = {}

    def _load_models(self):
        """Load all available trained models."""
        model_files = {
            'gradient_boosting': 'gradient_boosting_model.joblib',
            'random_forest': 'random_forest_model.joblib', 
            'logistic_regression': 'logistic_regression_model.joblib',
            'label_correction': 'label_correction_model.joblib'
        }
        
        for model_name, filename in model_files.items():
            model_path = self.models_dir / filename
            if model_path.exists():
                try:
                    self.models[model_name] = joblib.load(model_path)
                    logging.info(f"Loaded model: {model_name}")
                except Exception as e:
                    logging.error(f"Failed to load model {model_name}: {e}")
            else:
                logging.warning(f"Model file not found: {model_path}")

    def get_available_models(self) -> List[str]:
        """Get list of available models."""
        return list(self.models.keys())

    def get_best_model_name(self) -> str:
        """Get the name of the best performing model based on results and availability."""
        if not self.model_metadata:
            # Return best available model as fallback
            if 'random_forest' in self.models:
                return 'random_forest'
            elif 'logistic_regression' in self.models:
                return 'logistic_regression'
            else:
                return list(self.models.keys())[0] if self.models else 'gradient_boosting'

        models_results = self.model_metadata.get('models', {})
        best_model = 'gradient_boosting'
        best_accuracy = 0

        # Find best performing model that is actually available
        for model_name, results in models_results.items():
            if model_name in self.models:  # Only consider loaded models
                accuracy = results.get('test_accuracy', 0)
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_model = model_name

        # If best model from results isn't available, use best available model
        if best_model not in self.models:
            if 'random_forest' in self.models:
                return 'random_forest'
            elif 'logistic_regression' in self.models:
                return 'logistic_regression'
            else:
                return list(self.models.keys())[0] if self.models else 'gradient_boosting'

        return best_model

    def get_model_performance(self, model_name: str) -> Dict[str, Any]:
        """
        Get performance metrics for a specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dictionary containing performance metrics
        """
        if model_name not in self.model_metadata.get('models', {}):
            return {}
            
        return self.model_metadata['models'][model_name]

    def predict(self, X: np.ndarray, model_name: str = None) -> Dict[str, Any]:
        """
        Make prediction using specified model.
        
        Args:
            X: Feature array (preprocessed)
            model_name: Name of model to use (defaults to best model)
            
        Returns:
            Dictionary containing prediction results
        """
        if model_name is None:
            model_name = self.get_best_model_name()
            
        if model_name not in self.models:
            raise ValueError(f"Model '{model_name}' not available. Available models: {list(self.models.keys())}")
            
        model = self.models[model_name]
        
        # Make prediction
        prediction = model.predict(X)[0]
        
        # Get prediction probability if available
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(X)[0]
            prob_negative = probabilities[0]
            prob_positive = probabilities[1]
        else:
            # For models without predict_proba, use decision function or default
            prob_positive = 0.5 if prediction == 1 else 0.3
            prob_negative = 1 - prob_positive
            
        # Get feature importance if available
        feature_importance = self._get_feature_importance(model_name)
        
        return {
            'prediction': int(prediction),
            'prediction_label': 'Emerging Expert' if prediction == 1 else 'Not Emerging Expert',
            'probability_positive': prob_positive,
            'probability_negative': prob_negative,
            'confidence': max(prob_positive, prob_negative),
            'model_used': model_name,
            'feature_importance': feature_importance,
            'model_performance': self.get_model_performance(model_name)
        }

    def _get_feature_importance(self, model_name: str) -> Dict[str, float]:
        """Get feature importance for the specified model."""
        model_results = self.model_metadata.get('models', {}).get(model_name, {})
        
        # Try to get feature importance from results
        if 'feature_importance' in model_results:
            return model_results['feature_importance']
        elif 'feature_coefficients' in model_results:
            # For linear models, use absolute coefficients as importance
            coeffs = model_results['feature_coefficients']
            return {k: abs(v) for k, v in coeffs.items()}
        else:
            return {}

    def get_prediction_explanation(self, prediction_result: Dict[str, Any],
                                 feature_values: Dict[str, Any]) -> str:
        """
        Generate human-readable explanation of the prediction.

        Args:
            prediction_result: Result from predict() method
            feature_values: Original feature values

        Returns:
            Explanation string
        """
        prediction = prediction_result['prediction']
        confidence = prediction_result['confidence']
        prob_positive = prediction_result['probability_positive']

        explanation = f"**Prediction: {prediction_result['prediction_label']}**\n\n"
        explanation += f"**Confidence: {confidence:.1%}**\n\n"

        if prediction == 1:
            explanation += f"This healthcare professional has a **{prob_positive:.1%} probability** of being an emerging expert.\n\n"
            explanation += "**Key Contributing Factors:**\n"
        else:
            explanation += f"This healthcare professional has a **{prob_positive:.1%} probability** of being an emerging expert.\n\n"
            explanation += "**Areas for Potential Growth:**\n"

        # Add feature-based insights
        feature_importance = prediction_result.get('feature_importance', {})

        # Publications insight
        pub_count = feature_values.get('publications_count', 0)
        if pub_count > 10:
            explanation += f"- **Strong publication record** ({pub_count} publications)\n"
        elif pub_count > 5:
            explanation += f"- **Moderate publication activity** ({pub_count} publications)\n"
        else:
            explanation += f"- **Limited publication activity** ({pub_count} publications)\n"

        # Clinical trials insight
        ct_count = feature_values.get('clinical_trials_count', 0)
        if ct_count > 2:
            explanation += f"- **Active in clinical research** ({ct_count} trials)\n"
        elif ct_count > 0:
            explanation += f"- **Some clinical trial involvement** ({ct_count} trials)\n"
        else:
            explanation += f"- **No clinical trial participation**\n"

        # Conference insight
        conf_count = feature_values.get('conferences_count', 0)
        if conf_count > 5:
            explanation += f"- **High conference engagement** ({conf_count} conferences)\n"
        elif conf_count > 2:
            explanation += f"- **Moderate conference participation** ({conf_count} conferences)\n"
        else:
            explanation += f"- **Limited conference activity** ({conf_count} conferences)\n"

        # Years of experience insight
        years_since = feature_values.get('years_since_enumeration')
        if years_since:
            if years_since < 3:
                explanation += f"- **Early career stage** ({years_since} years licensed)\n"
            elif years_since < 8:
                explanation += f"- **Mid-career stage** ({years_since} years licensed)\n"
            else:
                explanation += f"- **Experienced professional** ({years_since} years licensed)\n"

        return explanation

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about available models and their performance."""
        info = {
            'available_models': list(self.models.keys()),
            'best_model': self.get_best_model_name(),
            'model_performance': {}
        }
        
        for model_name in self.models.keys():
            if model_name in self.model_metadata.get('models', {}):
                perf = self.get_model_performance(model_name)
                info['model_performance'][model_name] = {
                    'accuracy': perf.get('test_accuracy', 0),
                    'roc_auc': perf.get('roc_auc', 0),
                    'f1_score': perf.get('classification_report', {}).get('weighted avg', {}).get('f1-score', 0)
                }
                
        return info
