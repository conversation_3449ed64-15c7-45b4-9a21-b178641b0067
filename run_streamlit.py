"""
Streamlit Application Launcher
Simple script to launch the Healthcare Emerging Expert Predictor Streamlit app.
"""

import subprocess
import sys
from pathlib import Path
import os


def check_requirements():
    """Check if required files and models exist."""
    required_files = [
        "models/gradient_boosting_model.joblib",
        "data/processed/feature_statistics.json",
        "data/processed/modeling_results.json",
        "streamlit_app/app.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 Please run the main ML pipeline first:")
        print("   python pipeline_orchestrator.py")
        return False
    
    return True


def install_streamlit_requirements():
    """Install Streamlit-specific requirements if needed."""
    requirements_path = Path("streamlit_app/requirements_streamlit.txt")
    
    if requirements_path.exists():
        print("📦 Installing Streamlit requirements...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_path)
            ])
            print("✅ Requirements installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install requirements: {e}")
            return False
    else:
        print("⚠️ Requirements file not found, assuming dependencies are installed")
        return True


def launch_streamlit():
    """Launch the Streamlit application."""
    app_path = Path("streamlit_app/app.py")
    
    if not app_path.exists():
        print(f"❌ Streamlit app not found: {app_path}")
        return False
    
    print("🚀 Launching Healthcare Emerging Expert Predictor...")
    print("📱 The app will open in your default browser")
    print("🔗 URL: http://localhost:8501")
    print("\n⏹️  Press Ctrl+C to stop the application")
    
    try:
        # Change to streamlit_app directory and run
        os.chdir("streamlit_app")
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to launch Streamlit: {e}")
        return False
    
    return True


def main():
    """Main launcher function."""
    print("🏥 Healthcare Emerging Expert Predictor - Streamlit Launcher")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Install Streamlit requirements
    if not install_streamlit_requirements():
        print("⚠️ Continuing anyway, but you may encounter import errors...")
    
    # Launch application
    if not launch_streamlit():
        sys.exit(1)


if __name__ == "__main__":
    main()
